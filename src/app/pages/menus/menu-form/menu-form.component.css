.menu-form-container {
  padding: 24px 32px 16px 32px;
  max-width: 1000px;
  margin: 0 auto;
  background: var(--apple-gray-50);
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.menu-form-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

/* Sections du formulaire */
.form-section {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--apple-gray-100);
}

.form-section:first-child {
  margin-bottom: 8px;
}

.form-section:last-of-type {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--apple-grenat);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* Layout du formulaire */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-item-full {
  flex: 1;
}

.form-item-half {
  flex: 1;
}

/* Styles des champs */
.form-input,
.form-textarea {
  border-radius: var(--radius-lg);
  border: 1px solid var(--apple-gray-200);
  transition: all 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  border-color: var(--apple-grenat);
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1);
}

.form-input-number {
  width: 100%;
}

:host ::ng-deep .form-input-number .ant-input-number {
  width: 100%;
  border-radius: var(--radius-lg);
  border: 1px solid var(--apple-gray-200);
}

:host ::ng-deep .form-input-number .ant-input-number:focus-within {
  border-color: var(--apple-grenat);
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1);
}

.form-select {
  width: 100%;
}

:host ::ng-deep .form-select .ant-select-selector {
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--apple-gray-200) !important;
}

:host ::ng-deep .form-select.ant-select-focused .ant-select-selector {
  border-color: var(--apple-grenat) !important;
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1) !important;
}

/* Éléments du menu */
.add-item-btn {
  border-radius: 8px;
  border: 2px dashed var(--apple-gray-300);
  color: var(--apple-grenat);
  transition: all 0.2s ease;
}

.add-item-btn:hover {
  border-color: var(--apple-grenat);
  background: var(--apple-grenat-soft);
}

.menu-items-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.menu-item-row {
  display: flex;
  align-items: stretch;
  gap: 12px;
  padding: 12px 16px;
  background: var(--apple-gray-50);
  border-radius: 12px;
  border: 1px solid var(--apple-gray-200);
  transition: all 0.2s ease;
  margin-bottom: 8px;
  min-height: 56px;
}

.menu-item-row:hover {
  background: var(--apple-white);
  border-color: var(--apple-grenat-pale);
}



.item-input {
  flex: 1;
  display: flex;
  align-items: center;
  width: 100%;
}

:host ::ng-deep .item-input .ant-form-item {
  width: 100% !important;
  margin-bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
}

:host ::ng-deep .item-input .ant-form-item-control {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

:host ::ng-deep .item-input .ant-form-item-control-input {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

:host ::ng-deep .item-input .ant-form-item-control-input-content {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

:host ::ng-deep .item-input .ant-input {
  width: 100% !important;
  height: 32px !important;
  border-radius: 8px !important;
  border: 1px solid var(--apple-gray-300) !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
}

:host ::ng-deep .item-input .ant-input:focus {
  border-color: var(--apple-grenat) !important;
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1) !important;
}

:host ::ng-deep .item-input .ant-form-item-explain {
  position: absolute !important;
  top: 100% !important;
  left: 0 !important;
  width: 100% !important;
  margin-top: 4px !important;
}

.item-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  height: 32px;
}

:host ::ng-deep .item-actions .ant-btn {
  height: 32px !important;
  width: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 8px !important;
  border: 1px solid var(--apple-gray-300) !important;
  transition: all 0.2s ease !important;
}

:host ::ng-deep .item-actions .ant-btn:hover {
  border-color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.1) !important;
}

.remove-item-btn {
  color: var(--apple-gray-400);
  transition: all 0.2s ease;
  border-radius: 6px;
}

.remove-item-btn:hover:not(:disabled) {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

.remove-item-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.empty-items {
  text-align: center;
  padding: 32px;
  color: var(--apple-gray-500);
  background: var(--apple-gray-50);
  border-radius: 12px;
  border: 2px dashed var(--apple-gray-300);
}

/* Actions du formulaire */
.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--apple-gray-200);
}

.cancel-btn {
  border-radius: 8px;
  border: 1px solid var(--apple-gray-300);
  color: var(--apple-gray-600);
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  border-color: var(--apple-grenat);
  color: var(--apple-grenat);
}

.submit-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(139, 21, 56, 0.3) !important;
}

.submit-btn:disabled {
  opacity: 0.6 !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Switch personnalisé */
:host ::ng-deep .ant-switch-checked {
  background-color: var(--apple-grenat) !important;
}

/* Checkbox personnalisé */
:host ::ng-deep .ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--apple-grenat) !important;
  border-color: var(--apple-grenat) !important;
}

/* Responsive */
@media (max-width: 768px) {
  .menu-form-container {
    padding: 16px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .menu-item-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .item-actions {
    align-self: flex-end;
  }
}

/* Container principal */
.menus-container {
  padding: 24px 32px 16px 32px;
  background: var(--apple-gray-50);
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.add-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 12px !important;
  height: 44px !important;
  padding: 0 24px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(139, 21, 56, 0.3) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.add-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(139, 21, 56, 0.4) !important;
}

/* Section de recherche */
.search-section {
  margin-bottom: 32px;
}

.search-row {
  display: flex;
  justify-content: center;
}

.search-input-group {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border-radius: var(--radius-lg);
  border: 2px solid var(--apple-gray-300);
  transition: all 0.3s ease;
  font-size: 16px;
  background: var(--apple-white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-input:focus {
  border-color: var(--apple-grenat);
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1);
  outline: none;
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--apple-gray-400);
  transition: all 0.2s ease;
  border: none;
  background: none;
}

.clear-search-btn:hover {
  color: var(--apple-grenat);
  background: var(--apple-grenat-pale);
}

/* Grille des menus */
.menus-grid {
  margin-bottom: 32px;
}

/* Cartes de menu Apple-like */
.menu-card {
  border-radius: 20px !important;
  border: 1px solid var(--apple-gray-200) !important;
  background: var(--apple-white) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.2s ease !important;
  overflow: hidden !important;
  height: 500px !important;
  display: flex !important;
  flex-direction: column !important;
}

.menu-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(139, 21, 56, 0.12) !important;
  border-color: var(--apple-grenat-pale) !important;
}

:host ::ng-deep .menu-card .ant-card-body {
  padding: 24px !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

:host ::ng-deep .menu-card .ant-card-actions {
  background: var(--apple-gray-50) !important;
  border-top: 1px solid var(--apple-gray-200) !important;
  padding: 12px 24px !important;
}

:host ::ng-deep .menu-card .ant-card-actions > li {
  margin: 0 8px !important;
}

:host ::ng-deep .menu-card .ant-card-actions > li > span {
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

:host ::ng-deep .menu-card .ant-card-actions > li > span:hover {
  background: var(--apple-grenat-pale) !important;
}

/* Header du menu */
.menu-header {
  margin-bottom: 20px;
  border-bottom: 1px solid var(--apple-gray-100);
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.menu-title {
  margin: 0;
  font-size: 22px;
  font-weight: 800;
  color: var(--apple-black);
  line-height: 1.2;
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
  flex: 1;
}

.menu-options-btn {
  color: var(--apple-gray-400) !important;
  transition: all 0.2s ease !important;
  padding: 4px !important;
  border-radius: 6px !important;
  flex-shrink: 0;
}

.menu-options-btn:hover {
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-pale) !important;
}

/* Styles pour le menu dropdown */
:host ::ng-deep .ant-dropdown-menu {
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  border: 1px solid var(--apple-gray-200) !important;
}

:host ::ng-deep .ant-dropdown-menu .ant-dropdown-menu-item {
  padding: 8px 16px !important;
  transition: all 0.2s ease !important;
}

:host ::ng-deep .ant-dropdown-menu .ant-dropdown-menu-item:hover {
  background: var(--apple-grenat-soft) !important;
}

:host ::ng-deep .ant-dropdown-menu .delete-item:hover {
  background: rgba(255, 77, 79, 0.1) !important;
  color: #ff4d4f !important;
}

/* Liste des éléments du menu */
.menu-items {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 370px;
  padding-right: 8px;
  word-wrap: break-word;
}

.menu-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--apple-gray-50);
  transition: all 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background: var(--apple-grenat-soft);
  margin: 0 -12px;
  padding: 8px 12px;
  border-radius: 8px;
}

.check-icon {
  color: var(--apple-grenat);
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.check-icon.checked {
  color: var(--apple-grenat);
}

.item-text {
  font-size: 14px;
  color: var(--apple-gray-700);
  line-height: 1.4;
  flex: 1;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* État vide */
.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.empty-card {
  border-radius: 20px !important;
  border: 2px dashed var(--apple-gray-300) !important;
  background: var(--apple-white) !important;
  max-width: 400px !important;
  width: 100% !important;
}

.empty-content {
  text-align: center;
  padding: 32px 24px;
}

.empty-icon {
  font-size: 64px;
  color: var(--apple-gray-400);
  margin-bottom: 16px;
}

.empty-content h3 {
  margin: 16px 0 8px 0;
  color: var(--apple-gray-700);
  font-weight: 600;
}

.empty-content p {
  margin-bottom: 24px;
  color: var(--apple-gray-500);
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
  .menus-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .page-title {
    font-size: 24px;
    text-align: center;
  }
  
  .search-input-group {
    max-width: 100%;
  }
}

@media (max-width: 576px) {
  .menu-card:hover {
    transform: none !important;
  }
  
  .menu-title {
    font-size: 18px;
  }
  
  .menu-price {
    font-size: 16px;
  }
}

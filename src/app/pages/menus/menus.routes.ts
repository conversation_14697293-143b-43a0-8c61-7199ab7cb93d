import { Routes } from '@angular/router';

export const MENUS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./menus-list/menus-list.component').then(m => m.MenusListComponent),
    data: { breadcrumb: { label: 'Liste des menus' } }
  },
  {
    path: 'new',
    loadComponent: () => import('./menu-form/menu-form.component').then(m => m.MenuFormComponent),
    data: { breadcrumb: { label: 'Nouveau menu', icon: 'plus' } }
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./menu-form/menu-form.component').then(m => m.MenuFormComponent),
    data: { breadcrumb: { label: 'Modifier menu', icon: 'edit' } }
  }
];

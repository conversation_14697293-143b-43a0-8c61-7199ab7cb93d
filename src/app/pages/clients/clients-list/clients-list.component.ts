import {Component, OnInit, signal} from '@angular/core';
import {CommonModule} from '@angular/common';
import {RouterLink} from '@angular/router';
import {FormsModule} from '@angular/forms';

// Ng-Zorro imports
import {NzTableModule} from 'ng-zorro-antd/table';
import {NzButtonModule} from 'ng-zorro-antd/button';
import {NzInputModule} from 'ng-zorro-antd/input';
import {NzSelectModule} from 'ng-zorro-antd/select';
import {NzIconModule} from 'ng-zorro-antd/icon';
import {NzTagModule} from 'ng-zorro-antd/tag';
import {NzCardModule} from 'ng-zorro-antd/card';
import {NzPaginationModule} from 'ng-zorro-antd/pagination';
import {NzDividerModule} from 'ng-zorro-antd/divider';
import {NzCheckboxModule} from 'ng-zorro-antd/checkbox';
import {NzModalModule, NzModalService} from 'ng-zorro-antd/modal';
import {NzMessageService} from 'ng-zorro-antd/message';
import {NzToolTipModule} from 'ng-zorro-antd/tooltip';
import {NzDatePickerModule} from 'ng-zorro-antd/date-picker';
import {NzDropDownModule} from 'ng-zorro-antd/dropdown';
import {NzMenuModule} from 'ng-zorro-antd/menu';

interface Client {
  id: string;
  nom: string;
  prenom: string;
  cin: string;
  ville: string;
  adresse: string;
  telephone1: string;
  telephone2?: string;
  email: string;
  dateCreation: Date;
  nombreEvenements: number;
  checked?: boolean;
}

@Component({
  selector: 'app-clients-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FormsModule,
    NzTableModule,
    NzButtonModule,
    NzInputModule,
    NzSelectModule,
    NzIconModule,
    NzTagModule,
    NzCardModule,
    NzPaginationModule,
    NzDividerModule,
    NzCheckboxModule,
    NzModalModule,
    NzToolTipModule,
    NzDatePickerModule,
    NzDropDownModule,
    NzMenuModule
  ],
  templateUrl: './clients-list.component.html',
  styleUrl: './clients-list.component.css'
})
export class ClientsListComponent implements OnInit {
  constructor(
    private message: NzMessageService,
    private modal: NzModalService
  ) {}

  clients: Client[] = [];
  filteredClients: Client[] = [];
  loading = false;

  // Filtres - exactement comme events
  searchText = '';
  selectedVille = '';
  selectedStatus = '';
  selectedDateRange: Date[] = [];

  // Checkbox selection - exactement comme events
  allChecked = false;
  indeterminate = false;
  selectedClients: Client[] = [];

  // Options pour les filtres
  villes = ['Casablanca', 'Rabat', 'Marrakech', 'Fès', 'Tanger', 'Agadir', 'Meknès', 'Oujda', 'Kenitra', 'Tétouan'];

  // Computed properties
  get totalEvents(): number {
    return this.filteredClients.reduce((sum, client) => sum + client.nombreEvenements, 0);
  }

  ngOnInit(): void {
    this.loadClients();
  }

  loadClients(): void {
    this.loading = true;

    // Simulation de données
    setTimeout(() => {
      this.clients = [
        {
          id: '1',
          nom: 'Benali',
          prenom: 'Ahmed',
          cin: 'AB123456',
          ville: 'Casablanca',
          adresse: '123 Rue Mohammed V',
          telephone1: '0612345678',
          telephone2: '0522123456',
          email: '<EMAIL>',
          dateCreation: new Date('2024-01-15'),
          nombreEvenements: 3
        },
        {
          id: '2',
          nom: 'Alami',
          prenom: 'Fatima',
          cin: 'CD789012',
          ville: 'Rabat',
          adresse: '456 Avenue Hassan II',
          telephone1: '0623456789',
          email: '<EMAIL>',
          dateCreation: new Date('2024-02-20'),
          nombreEvenements: 1
        },
        {
          id: '3',
          nom: 'Tazi',
          prenom: 'Omar',
          cin: 'EF345678',
          ville: 'Marrakech',
          adresse: '789 Boulevard Zerktouni',
          telephone1: '0634567890',
          telephone2: '0524567890',
          email: '<EMAIL>',
          dateCreation: new Date('2024-03-10'),
          nombreEvenements: 5
        },
        {
          id: '4',
          nom: 'Idrissi',
          prenom: 'Aicha',
          cin: 'GH901234',
          ville: 'Fès',
          adresse: '321 Rue Talaa Kebira',
          telephone1: '0645678901',
          email: '<EMAIL>',
          dateCreation: new Date('2024-01-25'),
          nombreEvenements: 2
        },
        {
          id: '5',
          nom: 'Benjelloun',
          prenom: 'Youssef',
          cin: 'IJ567890',
          ville: 'Tanger',
          adresse: '654 Avenue Pasteur',
          telephone1: '0656789012',
          telephone2: '0539123456',
          email: '<EMAIL>',
          dateCreation: new Date('2024-02-05'),
          nombreEvenements: 4
        }
      ];

      this.applyFilters();
      this.loading = false;
    }, 500);
  }

  // Méthodes de filtrage - exactement comme events
  updateSingleChecked(): void {
    this.applyFilters();
    this.updateCheckboxState();
  }

  private applyFilters(): void {
    let filtered = [...this.clients];

    // Filtre par terme de recherche
    if (this.searchText.trim()) {
      const searchLower = this.searchText.toLowerCase().trim();
      filtered = filtered.filter(client =>
        client.nom.toLowerCase().includes(searchLower) ||
        client.prenom.toLowerCase().includes(searchLower) ||
        client.email.toLowerCase().includes(searchLower) ||
        client.cin.toLowerCase().includes(searchLower) ||
        client.telephone1.includes(searchLower)
      );
    }

    // Filtre par ville
    if (this.selectedVille) {
      filtered = filtered.filter(client => client.ville === this.selectedVille);
    }

    // Filtre par statut
    if (this.selectedStatus) {
      filtered = filtered.filter(client => {
        if (this.selectedStatus === 'actif') {
          return client.nombreEvenements > 0;
        } else {
          return client.nombreEvenements === 0;
        }
      });
    }

    // Filtre par date
    if (this.selectedDateRange && this.selectedDateRange.length === 2) {
      const [startDate, endDate] = this.selectedDateRange;
      filtered = filtered.filter(client => {
        const clientDate = new Date(client.dateCreation);
        return clientDate >= startDate && clientDate <= endDate;
      });
    }

    this.filteredClients = filtered;
  }

  clearFilters(): void {
    this.searchText = '';
    this.selectedVille = '';
    this.selectedStatus = '';
    this.selectedDateRange = [];
    this.updateSingleChecked();
  }

  // Méthodes de checkbox - exactement comme events
  updateAllChecked(): void {
    this.indeterminate = false;
    if (this.allChecked) {
      this.filteredClients = this.filteredClients.map(item => ({
        ...item,
        checked: true
      }));
    } else {
      this.filteredClients = this.filteredClients.map(item => ({
        ...item,
        checked: false
      }));
    }
    this.updateSelectedClients();
  }

  private updateCheckboxState(): void {
    const checkedCount = this.filteredClients.filter(item => item.checked).length;
    this.allChecked = checkedCount === this.filteredClients.length && this.filteredClients.length > 0;
    this.indeterminate = checkedCount > 0 && checkedCount < this.filteredClients.length;
    this.updateSelectedClients();
  }

  private updateSelectedClients(): void {
    this.selectedClients = this.filteredClients.filter(item => item.checked);
  }

  // Méthodes de tri - exactement comme events
  sortByNom = (a: Client, b: Client) => a.nom.localeCompare(b.nom);
  sortByPrenom = (a: Client, b: Client) => a.prenom.localeCompare(b.prenom);
  sortByCin = (a: Client, b: Client) => a.cin.localeCompare(b.cin);
  sortByVille = (a: Client, b: Client) => a.ville.localeCompare(b.ville);
  sortByTelephone = (a: Client, b: Client) => a.telephone1.localeCompare(b.telephone1);
  sortByEmail = (a: Client, b: Client) => a.email.localeCompare(b.email);
  sortByEvenements = (a: Client, b: Client) => a.nombreEvenements - b.nombreEvenements;

  // Méthodes d'action avec modales
  deleteSelectedClients(): void {
    const count = this.selectedClients.length;
    this.modal.confirm({
      nzTitle: 'Confirmer la suppression',
      nzContent: `Êtes-vous sûr de vouloir supprimer <strong>${count}</strong> client(s) ?<br><br>Cette action est irréversible.`,
      nzOkText: 'Supprimer',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        const selectedIds = this.selectedClients.map(client => client.id);
        this.clients = this.clients.filter(client => !selectedIds.includes(client.id));
        this.updateSingleChecked();
        this.message.success(`${count} client(s) supprimé(s) avec succès !`);
        console.log('Clients supprimés:', selectedIds);
      }
    });
  }

  deleteClient(clientId: string): void {
    const client = this.clients.find(c => c.id === clientId);
    if (!client) return;

    this.modal.confirm({
      nzTitle: 'Confirmer la suppression',
      nzContent: `Êtes-vous sûr de vouloir supprimer le client "<strong>${client.nom} ${client.prenom}</strong>" ?<br><br>Cette action est irréversible.`,
      nzOkText: 'Supprimer',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        this.clients = this.clients.filter(c => c.id !== clientId);
        this.updateSingleChecked();
        this.message.success(`Client "${client.nom} ${client.prenom}" supprimé avec succès !`);
        console.log('Client supprimé:', clientId);
      }
    });
  }

  getClientPhone(client: Client): string {
    return client.telephone2 ? `${client.telephone1} / ${client.telephone2}` : client.telephone1;
  }
}

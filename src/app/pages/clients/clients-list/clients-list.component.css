.clients-list-container {
  padding: 24px 32px 16px 32px;
  background: var(--apple-gray-50);
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.add-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 12px !important;
  height: 44px !important;
  padding: 0 24px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(139, 21, 56, 0.3) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.add-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(139, 21, 56, 0.4) !important;
}

.clients-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

/* Header section */
.header-section {
  margin-bottom: 24px;
}

.header-actions {
  display: flex;
  gap: 12px;
  justify-content: space-between;
  align-items: center;
}

.header-actions button {
  border-radius: 8px;
  font-weight: 500;
}

.right-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Filters section */
.filters-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.filters-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
  gap: 16px;
  align-items: center;
}

.filter-item {
  width: 100%;
}

.search-input {
  border-radius: var(--radius-lg);
  border: 1px solid var(--apple-gray-200);
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: var(--apple-grenat);
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1);
  outline: none;
}

.filters-section .ant-input,
.filters-section .ant-select-selector,
.filters-section .ant-picker,
.filters-section .ant-select,
.filters-section .ant-input-group {
  border-radius: 8px;
  width: 100% !important;
}

/* Table container */
.table-container {
  margin-bottom: 16px;
}

.clients-table {
  background: var(--apple-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--apple-gray-200) !important;
}

/* Table styles */
.clients-table .ant-table {
  border-radius: 12px;
}

.clients-table .ant-table-thead > tr > th {
  background: #f5f5f7 !important; /* Gris Apple doux */
  color: #1d1d1f !important;
  font-weight: 600;
  border-bottom: 1px solid #e5e5e7;
  text-align: left;
  border-radius: 0;
}

.clients-table .ant-table-thead > tr > th:first-child {
  border-top-left-radius: 12px;
}

.clients-table .ant-table-thead > tr > th:last-child {
  border-top-right-radius: 12px;
}

.clients-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
  vertical-align: middle;
}

.clients-table .ant-table-tbody > tr:hover > td {
  background: #f8f9fa;
}

.clients-table .ant-table-tbody > tr:last-child > td:first-child {
  border-bottom-left-radius: 12px;
}

.clients-table .ant-table-tbody > tr:last-child > td:last-child {
  border-bottom-right-radius: 12px;
}

/* Action menu */
.action-menu {
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  color: var(--apple-gray-600);
  transition: all 0.2s ease;
  cursor: pointer;
}

.action-trigger:hover {
  background: var(--apple-gray-100);
  color: var(--apple-grenat);
}

:host ::ng-deep .action-menu .ant-dropdown-menu {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: 1px solid var(--apple-gray-200);
}

:host ::ng-deep .action-menu .ant-dropdown-menu-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
  min-width: 140px;
}

:host ::ng-deep .action-menu .ant-dropdown-menu-item:hover {
  background: var(--apple-gray-50);
}

:host ::ng-deep .action-menu .danger-item:hover {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

/* Styles pour le menu dropdown */
:host ::ng-deep .ant-dropdown-menu {
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  border: 1px solid var(--apple-gray-200) !important;
}

:host ::ng-deep .ant-dropdown-menu .ant-dropdown-menu-item {
  padding: 8px 16px !important;
  transition: all 0.2s ease !important;
}

:host ::ng-deep .ant-dropdown-menu .ant-dropdown-menu-item:hover {
  background: var(--apple-grenat-soft) !important;
}

:host ::ng-deep .ant-dropdown-menu .delete-item:hover {
  background: rgba(255, 77, 79, 0.1) !important;
  color: #ff4d4f !important;
}

/* Client name link */
.client-name-link {
  color: var(--apple-grenat);
  text-decoration: none;
  transition: all 0.3s ease;
}

.client-name-link:hover {
  color: var(--apple-grenat-dark);
  text-decoration: underline;
}

.client-name-link strong {
  font-weight: 600;
}

/* Summary section */
.summary-section {
  margin-top: 16px;
  padding: 16px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-stats {
  display: flex;
  gap: 24px;
  justify-content: center;
  align-items: center;
}

.stat-item {
  font-size: 14px;
  color: #495057;
}

.stat-item strong {
  color: #212529;
  font-weight: 600;
}

/* No results */
.no-results {
  text-align: center;
  padding: 48px 24px;
  color: #6c757d;
}

.no-results-icon {
  font-size: 64px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.no-results h3 {
  margin: 16px 0 8px 0;
  color: #495057;
  font-weight: 600;
}

.no-results p {
  margin-bottom: 24px;
  color: #6c757d;
}

/* Responsive */
@media (max-width: 1200px) {
  .filters-grid {
    grid-template-columns: 2fr 1fr 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .clients-list-container {
    padding: 16px;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .header-actions {
    flex-direction: column;
    gap: 16px;
  }

  .summary-stats {
    flex-direction: column;
    gap: 12px;
  }
}

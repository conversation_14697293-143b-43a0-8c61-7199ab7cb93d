import { Routes } from '@angular/router';

export const CLIENTS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./clients-list/clients-list.component').then(m => m.ClientsListComponent),
    data: { breadcrumb: { label: 'Liste des clients' } }
  },
  {
    path: 'new',
    loadComponent: () => import('./client-form-page/client-form-page.component').then(m => m.ClientFormPageComponent),
    data: { breadcrumb: { label: 'Nouveau client', icon: 'plus' } }
  },
  {
    path: 'details/:id',
    loadComponent: () => import('./client-details/client-details.component').then(m => m.ClientDetailsComponent),
    data: { breadcrumb: { label: 'Détails client', icon: 'eye' } }
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./client-form-page/client-form-page.component').then(m => m.ClientFormPageComponent),
    data: { breadcrumb: { label: 'Modifier client', icon: 'edit' } }
  }
];

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzSpinModule } from 'ng-zorro-antd/spin';


interface Client {
  id: string;
  nom: string;
  prenom: string;
  cin: string;
  ville: string;
  adresse: string;
  telephone1: string;
  telephone2?: string;
  email: string;
  dateCreation: Date;
  nombreEvenements: number;
}

interface Event {
  id: number;
  type: string;
  category: string;
  client: string;
  date: Date;
  personnes: number;
  ville: string;
  montant: number;
  status: string;
}

@Component({
  selector: 'app-client-details',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzTagModule,
    NzDividerModule,
    NzDescriptionsModule,
    NzTableModule,
    NzEmptyModule,
    NzSpinModule,

  ],
  templateUrl: './client-details.component.html',
  styleUrl: './client-details.component.css'
})
export class ClientDetailsComponent implements OnInit {
  clientId: string | null = null;
  client: Client | null = null;
  clientEvents: Event[] = [];
  loading = true;

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.clientId = this.route.snapshot.paramMap.get('id');
    if (this.clientId) {
      this.loadClientDetails();
    } else {
      this.router.navigate(['/clients']);
    }
  }

  private loadClientDetails(): void {
    this.loading = true;
    
    // Simulation d'un appel API
    setTimeout(() => {
      // Données simulées - à remplacer par un vrai service
      this.client = {
        id: this.clientId!,
        nom: 'Alami',
        prenom: 'Fatima',
        cin: 'AB123456',
        ville: 'Casablanca',
        adresse: '123 Rue Mohammed V, Casablanca',
        telephone1: '0612345678',
        telephone2: '0522123456',
        email: '<EMAIL>',
        dateCreation: new Date('2024-01-15'),
        nombreEvenements: 3
      };

      this.clientEvents = [
        {
          id: 1,
          type: 'Mariage',
          category: 'particulier',
          client: 'Fatima Alami',
          date: new Date('2024-06-15'),
          personnes: 150,
          ville: 'Casablanca',
          montant: 45000,
          status: 'confirmé'
        },
        {
          id: 2,
          type: 'Anniversaire',
          category: 'particulier',
          client: 'Fatima Alami',
          date: new Date('2024-03-20'),
          personnes: 50,
          ville: 'Casablanca',
          montant: 12000,
          status: 'terminé'
        },
        {
          id: 3,
          type: 'Réception',
          category: 'particulier',
          client: 'Fatima Alami',
          date: new Date('2024-12-10'),
          personnes: 80,
          ville: 'Rabat',
          montant: 25000,
          status: 'en_attente'
        }
      ];

      this.loading = false;
    }, 1000);
  }

  getStatusColor(status: string): string {
    const colors: { [key: string]: string } = {
      'confirmé': 'green',
      'en_attente': 'orange',
      'terminé': 'blue',
      'annulé': 'red'
    };
    return colors[status] || 'default';
  }

  getStatusText(status: string): string {
    const texts: { [key: string]: string } = {
      'confirmé': 'Confirmé',
      'en_attente': 'En attente',
      'terminé': 'Terminé',
      'annulé': 'Annulé'
    };
    return texts[status] || status;
  }

  getTotalMontant(): number {
    return this.clientEvents.reduce((total, event) => total + event.montant, 0);
  }

  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount);
  }

  editClient(): void {
    this.router.navigate(['/clients/edit', this.clientId]);
  }

  goToEvent(eventId: number): void {
    this.router.navigate(['/events/details', eventId]);
  }

  goBack(): void {
    this.router.navigate(['/clients']);
  }
}

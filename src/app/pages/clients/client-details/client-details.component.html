<div class="client-details-container">

  <!-- Loading -->
  @if (loading) {
    <div class="loading-container">
      <nz-spin nzSize="large">
        <div class="loading-content">
          <p>Chargement des détails du client...</p>
        </div>
      </nz-spin>
    </div>
  }

  <!-- Content -->
  @if (!loading && client) {
    <!-- Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="client-info">
          <div class="client-avatar">
            <nz-icon nzType="user" nzTheme="outline"></nz-icon>
          </div>
          <div class="client-title">
            <h1>{{ client.nom }} {{ client.prenom }}</h1>
            <p class="client-subtitle">Client depuis le {{ formatDate(client.dateCreation) }}</p>
          </div>
        </div>
        <div class="header-actions">
          <button nz-button nzType="default" (click)="goBack()" class="back-btn">
            <nz-icon nzType="left"></nz-icon>
            Retour
          </button>
          <button nz-button nzType="primary" (click)="editClient()">
            <nz-icon nzType="edit"></nz-icon>
            Modifier
          </button>
        </div>
      </div>
    </div>

    <!-- Client Details Card -->
    <nz-card class="details-card" nzTitle="Informations personnelles">
      <nz-descriptions nzBordered [nzColumn]="2">
        <nz-descriptions-item nzTitle="Nom complet">
          {{ client.nom }} {{ client.prenom }}
        </nz-descriptions-item>
        <nz-descriptions-item nzTitle="CIN">
          {{ client.cin }}
        </nz-descriptions-item>
        <nz-descriptions-item nzTitle="Ville">
          <nz-tag nzColor="blue">{{ client.ville }}</nz-tag>
        </nz-descriptions-item>
        <nz-descriptions-item nzTitle="Téléphone principal">
          <a href="tel:{{ client.telephone1 }}">{{ client.telephone1 }}</a>
        </nz-descriptions-item>
        @if (client.telephone2) {
          <nz-descriptions-item nzTitle="Téléphone secondaire">
            <a href="tel:{{ client.telephone2 }}">{{ client.telephone2 }}</a>
          </nz-descriptions-item>
        }
        <nz-descriptions-item nzTitle="Email">
          <a href="mailto:{{ client.email }}">{{ client.email }}</a>
        </nz-descriptions-item>
        <nz-descriptions-item nzTitle="Adresse" [nzSpan]="2">
          {{ client.adresse }}
        </nz-descriptions-item>
      </nz-descriptions>
    </nz-card>

    <!-- Statistics Card -->
    <nz-card class="stats-card" nzTitle="Statistiques">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon events">
            <nz-icon nzType="calendar"></nz-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ client.nombreEvenements }}</div>
            <div class="stat-label">Événements</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon revenue">
            <nz-icon nzType="dollar"></nz-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatCurrency(getTotalMontant()) }}</div>
            <div class="stat-label">Chiffre d'affaires total</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon average">
            <nz-icon nzType="bar-chart"></nz-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatCurrency(getTotalMontant() / client.nombreEvenements) }}</div>
            <div class="stat-label">Montant moyen</div>
          </div>
        </div>
      </div>
    </nz-card>

    <!-- Events History Card -->
    <nz-card class="events-card" nzTitle="Historique des événements">
      @if (clientEvents.length > 0) {
        <nz-table [nzData]="clientEvents" [nzPageSize]="10" nzBordered class="events-table">
          <thead>
            <tr>
              <th>Type</th>
              <th>Date</th>
              <th>Personnes</th>
              <th>Ville</th>
              <th>Montant</th>
              <th>Statut</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            @for (event of clientEvents; track event.id) {
              <tr>
                <td>
                  <strong>{{ event.type }}</strong>
                </td>
                <td>{{ formatDate(event.date) }}</td>
                <td>{{ event.personnes }}</td>
                <td>{{ event.ville }}</td>
                <td>{{ formatCurrency(event.montant) }}</td>
                <td>
                  <nz-tag [nzColor]="getStatusColor(event.status)">
                    {{ getStatusText(event.status) }}
                  </nz-tag>
                </td>
                <td>
                  <button nz-button nzType="link" nzSize="small" (click)="goToEvent(event.id)">
                    <nz-icon nzType="eye"></nz-icon>
                    Voir
                  </button>
                </td>
              </tr>
            }
          </tbody>
        </nz-table>
      } @else {
        <nz-empty 
          nzNotFoundImage="simple" 
          nzNotFoundContent="Aucun événement trouvé pour ce client">
          <div nz-empty-footer>
            <button nz-button nzType="primary" routerLink="/events/new">
              <nz-icon nzType="plus"></nz-icon>
              Créer un événement
            </button>
          </div>
        </nz-empty>
      }
    </nz-card>
  }

  <!-- Error state -->
  @if (!loading && !client) {
    <nz-card class="error-card">
      <nz-empty 
        nzNotFoundImage="simple" 
        nzNotFoundContent="Client introuvable">
        <div nz-empty-footer>
          <button nz-button nzType="primary" (click)="goBack()">
            <nz-icon nzType="arrow-left"></nz-icon>
            Retour à la liste
          </button>
        </div>
      </nz-empty>
    </nz-card>
  }
</div>

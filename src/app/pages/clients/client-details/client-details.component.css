/* Container principal */
.client-details-container {
  padding: 24px;
  background: var(--apple-gray-50);
  min-height: 100vh;
}



/* Loading */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  text-align: center;
  color: var(--apple-gray-600);
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, var(--apple-grenat-pale) 0%, #f8f9fa 100%);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--apple-gray-200);
}

.client-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.client-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--apple-grenat-pale);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: var(--apple-grenat);
  box-shadow: var(--shadow-md);
}

.client-title h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
}

.client-subtitle {
  margin: 4px 0 0 0;
  color: var(--apple-gray-600);
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.back-btn {
  border-color: var(--apple-grenat);
  color: var(--apple-grenat);
  background: white;
  font-weight: 500;
}

.back-btn:hover {
  background: var(--apple-grenat);
  color: white;
  border-color: var(--apple-grenat);
}

/* Cards */
.details-card,
.stats-card,
.events-card,
.error-card {
  margin-bottom: 24px;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--apple-gray-200);
}

.details-card .ant-card-head,
.stats-card .ant-card-head,
.events-card .ant-card-head {
  background: var(--gradient-secondary);
  border-bottom: 1px solid var(--apple-gray-200);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.details-card .ant-card-head-title,
.stats-card .ant-card-head-title,
.events-card .ant-card-head-title {
  color: var(--apple-black);
  font-weight: 600;
}

/* Descriptions */
.ant-descriptions-item-label {
  font-weight: 600;
  color: var(--apple-gray-700);
}

.ant-descriptions-item-content {
  color: var(--apple-black);
}

.ant-descriptions-item-content a {
  color: var(--apple-grenat);
  text-decoration: none;
}

.ant-descriptions-item-content a:hover {
  color: var(--apple-grenat-dark);
  text-decoration: underline;
}

/* Statistics */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--gradient-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--apple-gray-200);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.events {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.average {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--apple-black);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--apple-gray-600);
  margin-top: 4px;
}

/* Events Table */
.events-table {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.events-table .ant-table-thead > tr > th {
  background: var(--apple-gray-50);
  border-bottom: 2px solid var(--apple-gray-200);
  font-weight: 600;
  color: var(--apple-gray-700);
}

.events-table .ant-table-tbody > tr:hover > td {
  background: var(--apple-grenat-pale) !important;
}

.events-table .ant-btn-link {
  color: var(--apple-grenat);
  padding: 0;
}

.events-table .ant-btn-link:hover {
  color: var(--apple-grenat-dark);
}

/* Tags */
.ant-tag {
  border-radius: var(--radius-md);
  font-weight: 500;
}

/* Empty states */
.ant-empty {
  padding: 40px 20px;
}

.ant-empty-footer {
  margin-top: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .client-details-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .client-info {
    flex-direction: column;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-item {
    justify-content: center;
    text-align: center;
  }

  .events-table {
    font-size: 12px;
  }

  .client-title h1 {
    font-size: 24px;
  }
}

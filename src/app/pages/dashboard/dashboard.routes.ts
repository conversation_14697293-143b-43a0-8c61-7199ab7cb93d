import { Routes } from '@angular/router';

export const DASHBOARD_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'overview',
    pathMatch: 'full'
  },
  {
    path: 'overview',
    loadComponent: () => import('./dashboard.component').then(m => m.DashboardComponent),
    data: { breadcrumb: { label: 'Vue d\'ensemble' } }
  },
  {
    path: 'statistics',
    loadComponent: () => import('./statistics/statistics.component').then(m => m.StatisticsComponent),
    data: { breadcrumb: { label: 'Statistiques' } }
  },
  {
    path: 'reports',
    loadComponent: () => import('./reports/reports.component').then(m => m.ReportsComponent),
    data: { breadcrumb: { label: 'Rapports' } }
  }
];

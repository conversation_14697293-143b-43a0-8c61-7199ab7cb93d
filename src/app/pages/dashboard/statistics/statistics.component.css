.statistics-container {
  padding: 24px 32px 16px 32px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  min-height: 100vh;
}

/* Header */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--apple-gray-600);
}

.header-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.period-select {
  width: 150px;
}

.export-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
}

/* Statistiques avancées */
.advanced-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.advanced-stat-card {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.advanced-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(139, 21, 56, 0.2);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-icon svg {
  width: 28px;
  height: 28px;
}

.revenue-icon {
  background: linear-gradient(135deg, rgba(139, 21, 56, 0.1), rgba(169, 29, 66, 0.1));
  color: #8B1538;
}

.conversion-icon {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 209, 88, 0.1));
  color: #34c759;
}

.satisfaction-icon {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(0, 86, 204, 0.1));
  color: #007aff;
}

.repeat-icon {
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.1), rgba(255, 140, 0, 0.1));
  color: #ff9500;
}

.stat-info {
  flex: 1;
}

:host ::ng-deep .advanced-stat-card .ant-statistic-title {
  color: var(--apple-gray-600) !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
}

:host ::ng-deep .advanced-stat-card .ant-statistic-content {
  color: var(--apple-gray-900) !important;
  font-size: 24px !important;
  font-weight: 700 !important;
}

/* Graphiques */
.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-card {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.chart-header {
  margin-bottom: 24px;
}

.chart-title {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.chart-subtitle {
  margin: 0;
  font-size: 14px;
  color: var(--apple-gray-600);
}

/* Simulation de graphique en barres */
.pie-chart-simulation {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chart-bar {
  height: 8px;
  background: var(--apple-gray-200);
  border-radius: 4px;
  overflow: hidden;
}

.chart-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.chart-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.label-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.label-text {
  flex: 1;
  font-weight: 500;
  color: var(--apple-gray-800);
}

.label-value {
  font-weight: 600;
  color: var(--apple-gray-600);
}

/* Simulation de graphique linéaire */
.line-chart-simulation {
  display: flex;
  justify-content: space-between;
  align-items: end;
  height: 200px;
  padding: 20px 0;
  border-bottom: 1px solid var(--apple-gray-200);
}

.month-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.month-bars {
  display: flex;
  gap: 4px;
  align-items: end;
  height: 120px;
}

.events-bar,
.revenue-bar {
  width: 12px;
  height: 100%;
  background: var(--apple-gray-200);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.bar-fill {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-radius: 6px;
  transition: height 0.3s ease;
}

.bar-fill.events {
  background: var(--apple-grenat);
}

.bar-fill.revenue {
  background: #34c759;
}

.month-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--apple-gray-700);
}

.month-values {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  font-size: 10px;
  color: var(--apple-gray-500);
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--apple-gray-700);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.events {
  background: var(--apple-grenat);
}

.legend-color.revenue {
  background: #34c759;
}

/* Insights */
.insights-card {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.insights-header {
  margin-bottom: 24px;
}

.insights-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.insight-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid;
}

.insight-item.success {
  background: rgba(52, 199, 89, 0.05);
  border-left-color: #34c759;
}

.insight-item.info {
  background: rgba(0, 122, 255, 0.05);
  border-left-color: #007aff;
}

.insight-item.warning {
  background: rgba(255, 149, 0, 0.05);
  border-left-color: #ff9500;
}

.insight-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.insight-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.insight-content p {
  margin: 0;
  font-size: 14px;
  color: var(--apple-gray-600);
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .statistics-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-filters {
    justify-content: center;
  }
  
  .advanced-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
  }
}

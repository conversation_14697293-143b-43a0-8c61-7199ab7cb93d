<div class="statistics-container">
  <!-- Header avec filtres -->
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h2 class="page-title">Statistiques détaillées</h2>
        <p class="page-subtitle">Analyse approfondie de vos performances</p>
      </div>
      <div class="header-filters">
        <nz-select 
          [(ngModel)]="selectedPeriod" 
          (ngModelChange)="onPeriodChange()"
          class="period-select">
          <nz-option nzValue="week" nzLabel="Cette semaine"></nz-option>
          <nz-option nzValue="month" nzLabel="Ce mois"></nz-option>
          <nz-option nzValue="quarter" nzLabel="Ce trimestre"></nz-option>
          <nz-option nzValue="year" nzLabel="Cette année"></nz-option>
        </nz-select>
        <button 
          nz-button 
          nzType="primary" 
          (click)="exportData()"
          class="export-btn">
          <nz-icon nzType="download"></nz-icon>
          Exporter
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques avancées -->
  <div class="advanced-stats-grid">
    <nz-card class="advanced-stat-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon revenue-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.8 10.9C9.53 10.31 8.8 9.7 8.8 8.75C8.8 7.66 9.81 6.9 11.5 6.9C13.28 6.9 13.94 7.75 14 9H16.21C16.14 7.28 15.09 5.7 13 5.19V3H10V5.16C8.06 5.58 6.5 6.84 6.5 8.77C6.5 11.08 8.41 12.23 11.2 12.9C13.7 13.5 14.2 14.38 14.2 15.31C14.2 16 13.71 17.1 11.5 17.1C9.44 17.1 8.63 16.18 8.5 15H6.32C6.44 17.19 8.08 18.42 10 18.83V21H13V18.85C14.95 18.5 16.5 17.35 16.5 15.3C16.5 12.46 14.07 11.5 11.8 10.9Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <nz-statistic
            [nzValue]="formatCurrency(advancedStats.averageEventValue)"
            nzTitle="Valeur moyenne par événement">
          </nz-statistic>
        </div>
      </div>
    </nz-card>

    <nz-card class="advanced-stat-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon conversion-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 6L18.29 8.29L13.41 13.17L9.41 9.17L2 16.59L3.41 18L9.41 12L13.41 16L19.71 9.71L22 12V6H16Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="advancedStats.conversionRate" 
            nzSuffix="%" 
            nzTitle="Taux de conversion">
          </nz-statistic>
        </div>
      </div>
    </nz-card>

    <nz-card class="advanced-stat-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon satisfaction-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="advancedStats.customerSatisfaction" 
            nzSuffix="%" 
            nzTitle="Satisfaction client">
          </nz-statistic>
        </div>
      </div>
    </nz-card>

    <nz-card class="advanced-stat-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon repeat-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 4V1L8 5L12 9V6C15.31 6 18 8.69 18 12C18 13.01 17.75 13.97 17.3 14.8L18.76 16.26C19.54 15.03 20 13.57 20 12C20 7.58 16.42 4 12 4ZM12 18C8.69 18 6 15.31 6 12C6 10.99 6.25 10.03 6.7 9.2L5.24 7.74C4.46 8.97 4 10.43 4 12C4 16.42 7.58 20 12 20V23L16 19L12 15V18Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="advancedStats.repeatCustomers" 
            nzSuffix="%" 
            nzTitle="Clients récurrents">
          </nz-statistic>
        </div>
      </div>
    </nz-card>
  </div>

  <!-- Graphiques et analyses -->
  <div class="charts-grid">
    <!-- Répartition par type d'événement -->
    <nz-card class="chart-card" [nzLoading]="isLoading">
      <div class="chart-header">
        <h3 class="chart-title">Répartition par type d'événement</h3>
        <p class="chart-subtitle">{{ getTotalEvents() }} événements au total</p>
      </div>
      <div class="chart-content">
        <div class="pie-chart-simulation">
          @for (item of eventTypesData; track item.name) {
            <div class="chart-item">
              <div class="chart-bar">
                <div 
                  class="chart-fill" 
                  [style.width.%]="getPercentage(item.value, getTotalEvents())"
                  [style.background-color]="item.color">
                </div>
              </div>
              <div class="chart-label">
                <span class="label-color" [style.background-color]="item.color"></span>
                <span class="label-text">{{ item.name }}</span>
                <span class="label-value">{{ item.value }} ({{ getPercentage(item.value, getTotalEvents()) }}%)</span>
              </div>
            </div>
          }
        </div>
      </div>
    </nz-card>

    <!-- Évolution mensuelle -->
    <nz-card class="chart-card" [nzLoading]="isLoading">
      <div class="chart-header">
        <h3 class="chart-title">Évolution mensuelle</h3>
        <p class="chart-subtitle">Événements et chiffre d'affaires</p>
      </div>
      <div class="chart-content">
        <div class="line-chart-simulation">
          @for (month of monthlyData; track month.month) {
            <div class="month-data">
              <div class="month-bars">
                <div class="events-bar">
                  <div 
                    class="bar-fill events" 
                    [style.height.%]="(month.events / getMaxValue(monthlyData, 'events')) * 100">
                  </div>
                </div>
                <div class="revenue-bar">
                  <div 
                    class="bar-fill revenue" 
                    [style.height.%]="(month.revenue / getMaxValue(monthlyData, 'revenue')) * 100">
                  </div>
                </div>
              </div>
              <div class="month-label">{{ month.month }}</div>
              <div class="month-values">
                <div class="events-value">{{ month.events }} evt</div>
                <div class="revenue-value">{{ formatCurrency(month.revenue) }}</div>
              </div>
            </div>
          }
        </div>
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color events"></span>
            <span>Événements</span>
          </div>
          <div class="legend-item">
            <span class="legend-color revenue"></span>
            <span>Chiffre d'affaires</span>
          </div>
        </div>
      </div>
    </nz-card>
  </div>

  <!-- Insights et recommandations -->
  <nz-card class="insights-card">
    <div class="insights-header">
      <h3 class="insights-title">📊 Insights et recommandations</h3>
    </div>
    <div class="insights-grid">
      <div class="insight-item success">
        <div class="insight-icon">🎯</div>
        <div class="insight-content">
          <h4>Performance excellente</h4>
          <p>Votre taux de satisfaction client de {{ advancedStats.customerSatisfaction }}% est exceptionnel !</p>
        </div>
      </div>
      <div class="insight-item info">
        <div class="insight-icon">📈</div>
        <div class="insight-content">
          <h4>Croissance soutenue</h4>
          <p>Croissance de {{ advancedStats.growthRate }}% par rapport à la période précédente.</p>
        </div>
      </div>
      <div class="insight-item warning">
        <div class="insight-icon">💡</div>
        <div class="insight-content">
          <h4>Opportunité</h4>
          <p>{{ advancedStats.topEventType }} représente votre segment le plus rentable.</p>
        </div>
      </div>
    </div>
  </nz-card>
</div>

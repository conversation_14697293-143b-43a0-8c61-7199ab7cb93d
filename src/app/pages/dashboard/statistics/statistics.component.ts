import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

// Ng-Zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { FormsModule } from '@angular/forms';

interface ChartData {
  name: string;
  value: number;
  color: string;
}

interface MonthlyData {
  month: string;
  events: number;
  revenue: number;
  clients: number;
}

@Component({
  selector: 'app-statistics',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzStatisticModule,
    NzButtonModule,
    NzIconModule,
    NzProgressModule,
    NzSelectModule,
    NzDatePickerModule
  ],
  templateUrl: './statistics.component.html',
  styleUrl: './statistics.component.css'
})
export class StatisticsComponent implements OnInit {
  
  selectedPeriod = 'month';
  selectedYear = new Date().getFullYear();
  isLoading = false;

  // Données des graphiques
  eventTypesData: ChartData[] = [];
  monthlyData: MonthlyData[] = [];
  clientTypesData: ChartData[] = [];

  // Statistiques avancées
  advancedStats = {
    averageEventValue: 0,
    conversionRate: 0,
    customerSatisfaction: 0,
    repeatCustomers: 0,
    topEventType: '',
    topMonth: '',
    growthRate: 0
  };

  constructor() {}

  ngOnInit(): void {
    this.loadStatistics();
  }

  loadStatistics(): void {
    this.isLoading = true;

    // Simulation de données
    setTimeout(() => {
      this.eventTypesData = [
        { name: 'Mariages', value: 35, color: '#8B1538' },
        { name: 'Entreprise', value: 28, color: '#34c759' },
        { name: 'Anniversaires', value: 20, color: '#007aff' },
        { name: 'Fiançailles', value: 12, color: '#ff9500' },
        { name: 'Autres', value: 5, color: '#af52de' }
      ];

      this.clientTypesData = [
        { name: 'Particuliers', value: 65, color: '#8B1538' },
        { name: 'Entreprises', value: 35, color: '#34c759' }
      ];

      this.monthlyData = [
        { month: 'Jan', events: 12, revenue: 85000, clients: 8 },
        { month: 'Fév', events: 15, revenue: 102000, clients: 12 },
        { month: 'Mar', events: 18, revenue: 125000, clients: 15 },
        { month: 'Avr', events: 22, revenue: 145000, clients: 18 },
        { month: 'Mai', events: 25, revenue: 165000, clients: 20 },
        { month: 'Jun', events: 28, revenue: 185000, clients: 22 },
        { month: 'Jul', events: 32, revenue: 210000, clients: 25 },
        { month: 'Aoû', events: 30, revenue: 195000, clients: 24 },
        { month: 'Sep', events: 26, revenue: 175000, clients: 21 },
        { month: 'Oct', events: 24, revenue: 160000, clients: 19 },
        { month: 'Nov', events: 20, revenue: 140000, clients: 16 },
        { month: 'Déc', events: 18, revenue: 125000, clients: 14 }
      ];

      this.advancedStats = {
        averageEventValue: 6500,
        conversionRate: 78,
        customerSatisfaction: 94,
        repeatCustomers: 42,
        topEventType: 'Mariages',
        topMonth: 'Juillet',
        growthRate: 15.8
      };

      this.isLoading = false;
    }, 1000);
  }

  onPeriodChange(): void {
    this.loadStatistics();
  }

  exportData(): void {
    // Simulation d'export
    console.log('Export des données statistiques...');
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  getMaxValue(data: MonthlyData[], key: keyof MonthlyData): number {
    return Math.max(...data.map(item => Number(item[key])));
  }

  getPercentage(value: number, total: number): number {
    return Math.round((value / total) * 100);
  }

  getTotalEvents(): number {
    return this.eventTypesData.reduce((sum, item) => sum + item.value, 0);
  }

  getTotalRevenue(): number {
    return this.monthlyData.reduce((sum, item) => sum + item.revenue, 0);
  }
}

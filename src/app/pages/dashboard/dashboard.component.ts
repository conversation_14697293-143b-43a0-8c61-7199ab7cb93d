import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

// Ng-Zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';

interface DashboardStats {
  totalEvents: number;
  monthlyRevenue: number;
  activeClients: number;
  completionRate: number;
}

interface RecentEvent {
  id: string;
  title: string;
  client: string;
  date: string;
  status: 'confirmed' | 'pending' | 'completed';
  revenue: number;
  type: string;
}

interface QuickAction {
  title: string;
  description: string;
  icon: string;
  color: string;
  route: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzStatisticModule,
    NzButtonModule,
    NzIconModule,
    NzProgressModule,
    NzTableModule,
    NzTagModule,
    NzAvatarModule
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent implements OnInit {
  
  stats: DashboardStats = {
    totalEvents: 0,
    monthlyRevenue: 0,
    activeClients: 0,
    completionRate: 0
  };

  recentEvents: RecentEvent[] = [];
  quickActions: QuickAction[] = [];
  isLoading = false;

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.loadDashboardData();
    this.setupQuickActions();
  }

  loadDashboardData(): void {
    this.isLoading = true;

    // Simulation de données
    setTimeout(() => {
      this.stats = {
        totalEvents: 47,
        monthlyRevenue: 125000,
        activeClients: 23,
        completionRate: 94
      };

      this.recentEvents = [
        {
          id: '1',
          title: 'Mariage Alami',
          client: 'Famille Alami',
          date: '2025-01-15',
          status: 'confirmed',
          revenue: 45000,
          type: 'Mariage'
        },
        {
          id: '2',
          title: 'Séminaire TechCorp',
          client: 'TechCorp SARL',
          date: '2025-01-18',
          status: 'pending',
          revenue: 12000,
          type: 'Séminaire'
        },
        {
          id: '3',
          title: 'Anniversaire Ben Said',
          client: 'M. Ben Said',
          date: '2025-01-20',
          status: 'completed',
          revenue: 8500,
          type: 'Anniversaire'
        },
        {
          id: '4',
          title: 'Cocktail Inauguration',
          client: 'Société Moderne',
          date: '2025-01-22',
          status: 'confirmed',
          revenue: 18000,
          type: 'Cocktail'
        },
        {
          id: '5',
          title: 'Fiançailles Idrissi',
          client: 'Famille Idrissi',
          date: '2025-01-25',
          status: 'pending',
          revenue: 22000,
          type: 'Fiançailles'
        }
      ];

      this.isLoading = false;
    }, 800);
  }

  setupQuickActions(): void {
    this.quickActions = [
      {
        title: 'Nouvel événement',
        description: 'Créer un nouvel événement rapidement',
        icon: 'plus',
        color: '#8B1538',
        route: '/events/new'
      },
      {
        title: 'Calendrier',
        description: 'Voir le planning des événements',
        icon: 'calendar',
        color: '#34c759',
        route: '/events/calendar'
      },
      {
        title: 'Nouveau client',
        description: 'Ajouter un nouveau client',
        icon: 'user-add',
        color: '#007aff',
        route: '/clients/new'
      },
      {
        title: 'Nouveau menu',
        description: 'Créer un nouveau menu',
        icon: 'file-add',
        color: '#ff9500',
        route: '/menus/new'
      }
    ];
  }

  navigateToAction(route: string): void {
    this.router.navigate([route]);
  }

  viewAllEvents(): void {
    this.router.navigate(['/events']);
  }

  viewStatistics(): void {
    this.router.navigate(['/dashboard/statistics']);
  }

  viewReports(): void {
    this.router.navigate(['/dashboard/reports']);
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'confirmed': return 'green';
      case 'pending': return 'orange';
      case 'completed': return 'blue';
      default: return 'default';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'confirmed': return 'Confirmé';
      case 'pending': return 'En attente';
      case 'completed': return 'Terminé';
      default: return status;
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  }

  getClientInitials(clientName: string): string {
    return clientName
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }
}

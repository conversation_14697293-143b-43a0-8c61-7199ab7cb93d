import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

// Ng-Zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { FormsModule } from '@angular/forms';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  category: string;
  lastGenerated?: string;
}

interface GeneratedReport {
  id: string;
  name: string;
  type: string;
  generatedAt: string;
  size: string;
  status: 'ready' | 'generating' | 'error';
}

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzSelectModule,
    NzDatePickerModule,
    NzTableModule,
    NzTagModule,
    NzProgressModule
  ],
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.css'
})
export class ReportsComponent implements OnInit {
  
  selectedPeriod = 'month';
  dateRange: Date[] = [];
  isGenerating = false;

  reportTemplates: ReportTemplate[] = [
    {
      id: '1',
      name: 'Rapport financier',
      description: 'Analyse complète du chiffre d\'affaires et des bénéfices',
      icon: 'dollar',
      color: '#8B1538',
      category: 'Financier',
      lastGenerated: '2025-01-10'
    },
    {
      id: '2',
      name: 'Rapport d\'activité',
      description: 'Vue d\'ensemble des événements et performances',
      icon: 'bar-chart',
      color: '#34c759',
      category: 'Activité',
      lastGenerated: '2025-01-08'
    },
    {
      id: '3',
      name: 'Analyse clientèle',
      description: 'Segmentation et comportement des clients',
      icon: 'team',
      color: '#007aff',
      category: 'Clients',
      lastGenerated: '2025-01-05'
    },
    {
      id: '4',
      name: 'Rapport de satisfaction',
      description: 'Évaluation de la satisfaction client et qualité',
      icon: 'star',
      color: '#ff9500',
      category: 'Qualité'
    },
    {
      id: '5',
      name: 'Analyse des menus',
      description: 'Performance et popularité des menus proposés',
      icon: 'file-text',
      color: '#af52de',
      category: 'Produits',
      lastGenerated: '2025-01-03'
    },
    {
      id: '6',
      name: 'Rapport de stock',
      description: 'État des stocks et matériel de location',
      icon: 'inbox',
      color: '#32d74b',
      category: 'Inventaire'
    }
  ];

  generatedReports: GeneratedReport[] = [
    {
      id: '1',
      name: 'Rapport financier - Décembre 2024',
      type: 'PDF',
      generatedAt: '2025-01-10T14:30:00',
      size: '2.4 MB',
      status: 'ready'
    },
    {
      id: '2',
      name: 'Analyse clientèle - Q4 2024',
      type: 'Excel',
      generatedAt: '2025-01-08T09:15:00',
      size: '1.8 MB',
      status: 'ready'
    },
    {
      id: '3',
      name: 'Rapport d\'activité - Janvier 2025',
      type: 'PDF',
      generatedAt: '2025-01-05T16:45:00',
      size: '3.1 MB',
      status: 'generating'
    }
  ];

  constructor() {}

  ngOnInit(): void {
    // Initialiser la période par défaut
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    this.dateRange = [firstDay, now];
  }

  generateReport(template: ReportTemplate): void {
    this.isGenerating = true;
    
    // Simulation de génération
    setTimeout(() => {
      const newReport: GeneratedReport = {
        id: Date.now().toString(),
        name: `${template.name} - ${new Date().toLocaleDateString('fr-FR')}`,
        type: 'PDF',
        generatedAt: new Date().toISOString(),
        size: `${(Math.random() * 3 + 1).toFixed(1)} MB`,
        status: 'ready'
      };
      
      this.generatedReports.unshift(newReport);
      this.isGenerating = false;
      
      // Mettre à jour la date de dernière génération
      template.lastGenerated = new Date().toISOString().split('T')[0];
    }, 2000);
  }

  downloadReport(report: GeneratedReport): void {
    // Simulation de téléchargement
    console.log(`Téléchargement de ${report.name}`);
  }

  deleteReport(reportId: string): void {
    this.generatedReports = this.generatedReports.filter(r => r.id !== reportId);
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'ready': return 'green';
      case 'generating': return 'blue';
      case 'error': return 'red';
      default: return 'default';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'ready': return 'Prêt';
      case 'generating': return 'En cours...';
      case 'error': return 'Erreur';
      default: return status;
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getLastGeneratedText(dateString?: string): string {
    if (!dateString) return 'Jamais généré';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Aujourd\'hui';
    if (diffDays === 1) return 'Hier';
    if (diffDays < 7) return `Il y a ${diffDays} jours`;
    return date.toLocaleDateString('fr-FR');
  }

  scheduleReport(template: ReportTemplate): void {
    // Fonctionnalité future pour programmer des rapports automatiques
    console.log(`Programmer le rapport: ${template.name}`);
  }
}

.reports-container {
  padding: 24px 32px 16px 32px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  min-height: 100vh;
}

/* Header */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--apple-gray-600);
}

.header-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.date-range-picker,
.period-select {
  width: 150px;
}

/* Sections */
.reports-section,
.generated-section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.section-subtitle {
  margin: 0;
  font-size: 14px;
  color: var(--apple-gray-600);
}

/* Templates de rapports */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.template-card {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  padding: 24px;
}

.template-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(139, 21, 56, 0.2);
}

.template-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.template-header {
  display: flex;
  gap: 16px;
}

.template-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.template-icon svg {
  width: 24px;
  height: 24px;
}

.template-icon.icon-dollar {
  background: linear-gradient(135deg, rgba(139, 21, 56, 0.1), rgba(169, 29, 66, 0.1));
  color: #8B1538;
}

.template-icon.icon-bar-chart {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 209, 88, 0.1));
  color: #34c759;
}

.template-icon.icon-team {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(0, 86, 204, 0.1));
  color: #007aff;
}

.template-icon.icon-star {
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.1), rgba(255, 140, 0, 0.1));
  color: #ff9500;
}

.template-icon.icon-file-text {
  background: linear-gradient(135deg, rgba(175, 82, 222, 0.1), rgba(191, 90, 242, 0.1));
  color: #af52de;
}

.template-icon.icon-inbox {
  background: linear-gradient(135deg, rgba(50, 215, 75, 0.1), rgba(48, 209, 88, 0.1));
  color: #32d74b;
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.template-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--apple-gray-600);
  line-height: 1.5;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.template-category {
  padding: 4px 8px;
  background: var(--apple-gray-100);
  color: var(--apple-gray-700);
  border-radius: 6px;
  font-weight: 500;
}

.template-last-generated {
  color: var(--apple-gray-500);
}

.template-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.schedule-btn {
  border-radius: 8px !important;
  border: 1px solid var(--apple-gray-300) !important;
  color: var(--apple-gray-700) !important;
}

.schedule-btn:hover {
  border-color: var(--apple-grenat) !important;
  color: var(--apple-grenat) !important;
}

.generate-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
}

/* Table des rapports générés */
.reports-table-card {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  padding: 24px;
}

.report-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #ff4d4f;
  font-size: 16px;
}

.report-type {
  padding: 4px 8px;
  background: var(--apple-gray-100);
  color: var(--apple-gray-700);
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.report-actions {
  display: flex;
  gap: 4px;
}

.progress-bar {
  margin-top: 4px;
  width: 100px;
}

/* État vide */
.empty-reports {
  border-radius: 20px;
  border: 2px dashed rgba(139, 21, 56, 0.2);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  padding: 24px;
}

.empty-content {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--apple-gray-800);
}

.empty-content p {
  margin: 0;
  color: var(--apple-gray-600);
}

/* Conseils */
.tips-card {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  padding: 24px;
}

.tips-header {
  margin-bottom: 24px;
}

.tips-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.tip-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: var(--apple-gray-50);
  border-radius: 12px;
  border: 1px solid var(--apple-gray-200);
}

.tip-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.tip-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.tip-content p {
  margin: 0;
  font-size: 14px;
  color: var(--apple-gray-600);
  line-height: 1.5;
}

/* Styles ng-zorro personnalisés */
:host ::ng-deep .reports-table-card .ant-table-thead > tr > th {
  background: var(--apple-gray-50) !important;
  border-bottom: 2px solid var(--apple-gray-200) !important;
  color: var(--apple-grenat) !important;
  font-weight: 600 !important;
}

:host ::ng-deep .reports-table-card .ant-table-tbody > tr:hover > td {
  background: var(--apple-grenat-soft) !important;
}

/* Responsive */
@media (max-width: 1200px) {
  .templates-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .reports-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-filters {
    justify-content: center;
  }
  
  .tips-grid {
    grid-template-columns: 1fr;
  }
  
  .template-actions {
    justify-content: center;
  }
}

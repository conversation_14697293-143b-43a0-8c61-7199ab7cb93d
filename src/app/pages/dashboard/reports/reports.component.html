<div class="reports-container">
  <!-- Header avec filtres -->
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h2 class="page-title">Rapports et analyses</h2>
        <p class="page-subtitle">Générez et consultez vos rapports d'activité</p>
      </div>
      <div class="header-filters">
        <nz-range-picker 
          [(ngModel)]="dateRange"
          nzFormat="dd/MM/yyyy"
          class="date-range-picker">
        </nz-range-picker>
        <nz-select 
          [(ngModel)]="selectedPeriod"
          class="period-select">
          <nz-option nzValue="week" nzLabel="Cette semaine"></nz-option>
          <nz-option nzValue="month" nzLabel="Ce mois"></nz-option>
          <nz-option nzValue="quarter" nzLabel="Ce trimestre"></nz-option>
          <nz-option nzValue="year" nzLabel="Cette année"></nz-option>
        </nz-select>
      </div>
    </div>
  </div>

  <!-- Templates de rapports -->
  <div class="reports-section">
    <div class="section-header">
      <h3 class="section-title">📊 Templates de rapports</h3>
      <p class="section-subtitle">Choisissez le type de rapport à générer</p>
    </div>
    
    <div class="templates-grid">
      @for (template of reportTemplates; track template.id) {
        <div class="template-card" [class.loading]="isGenerating">
          <div class="template-content">
            <div class="template-header">
              <div class="template-icon" [class]="'icon-' + template.icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  @if (template.icon === 'dollar') {
                    <path d="M11.8 10.9C9.53 10.31 8.8 9.7 8.8 8.75C8.8 7.66 9.81 6.9 11.5 6.9C13.28 6.9 13.94 7.75 14 9H16.21C16.14 7.28 15.09 5.7 13 5.19V3H10V5.16C8.06 5.58 6.5 6.84 6.5 8.77C6.5 11.08 8.41 12.23 11.2 12.9C13.7 13.5 14.2 14.38 14.2 15.31C14.2 16 13.71 17.1 11.5 17.1C9.44 17.1 8.63 16.18 8.5 15H6.32C6.44 17.19 8.08 18.42 10 18.83V21H13V18.85C14.95 18.5 16.5 17.35 16.5 15.3C16.5 12.46 14.07 11.5 11.8 10.9Z" fill="currentColor"/>
                  } @else if (template.icon === 'bar-chart') {
                    <path d="M5 9.2H7V19H5V9.2ZM10.6 5H12.6V19H10.6V5ZM16.2 13H18.2V19H16.2V13Z" fill="currentColor"/>
                  } @else if (template.icon === 'team') {
                    <path d="M16 4C18.2 4 20 5.8 20 8C20 10.2 18.2 12 16 12C13.8 12 12 10.2 12 8C12 5.8 13.8 4 16 4ZM16 14C20.4 14 24 15.8 24 18V20H8V18C8 15.8 11.6 14 16 14ZM8 4C10.2 4 12 5.8 12 8C12 10.2 10.2 12 8 12C5.8 12 4 10.2 4 8C4 5.8 5.8 4 8 4ZM8 14C12.4 14 16 15.8 16 18V20H0V18C0 15.8 3.6 14 8 14Z" fill="currentColor"/>
                  } @else if (template.icon === 'star') {
                    <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" fill="currentColor"/>
                  } @else if (template.icon === 'file-text') {
                    <path d="M14 2H6C4.9 2 4.01 2.9 4.01 4L4 20C4 21.1 4.89 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z" fill="currentColor"/>
                  } @else if (template.icon === 'inbox') {
                    <path d="M19 3H4.99C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 15H15C15 16.66 13.65 18 12 18C10.35 18 9 16.66 9 15H5V5H19V15Z" fill="currentColor"/>
                  }
                </svg>
              </div>
              <div class="template-info">
                <h4 class="template-name">{{ template.name }}</h4>
                <p class="template-description">{{ template.description }}</p>
                <div class="template-meta">
                  <span class="template-category">{{ template.category }}</span>
                  <span class="template-last-generated">
                    {{ getLastGeneratedText(template.lastGenerated) }}
                  </span>
                </div>
              </div>
            </div>
            
            <div class="template-actions">
              <button 
                nz-button 
                nzType="default" 
                nzSize="small"
                (click)="scheduleReport(template)"
                class="schedule-btn">
                <nz-icon nzType="clock-circle"></nz-icon>
                Programmer
              </button>
              <button 
                nz-button 
                nzType="primary" 
                (click)="generateReport(template)"
                [nzLoading]="isGenerating"
                class="generate-btn">
                <nz-icon nzType="file-add"></nz-icon>
                Générer
              </button>
            </div>
          </div>
        </div>
      }
    </div>
  </div>

  <!-- Rapports générés -->
  <div class="generated-section">
    <div class="section-header">
      <h3 class="section-title">📁 Rapports générés</h3>
      <p class="section-subtitle">Historique de vos rapports récents</p>
    </div>

    @if (generatedReports.length > 0) {
      <div class="reports-table-card">
        <nz-table 
          [nzData]="generatedReports" 
          nzSize="middle"
          [nzShowPagination]="false">
          <thead>
            <tr>
              <th>Nom du rapport</th>
              <th>Type</th>
              <th>Date de génération</th>
              <th>Taille</th>
              <th>Statut</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            @for (report of generatedReports; track report.id) {
              <tr>
                <td>
                  <div class="report-name">
                    <nz-icon nzType="file-pdf" class="file-icon"></nz-icon>
                    {{ report.name }}
                  </div>
                </td>
                <td>
                  <span class="report-type">{{ report.type }}</span>
                </td>
                <td>{{ formatDate(report.generatedAt) }}</td>
                <td>{{ report.size }}</td>
                <td>
                  <nz-tag [nzColor]="getStatusColor(report.status)">
                    {{ getStatusText(report.status) }}
                  </nz-tag>
                  @if (report.status === 'generating') {
                    <nz-progress 
                      nzType="line" 
                      [nzPercent]="65" 
                      nzSize="small"
                      class="progress-bar">
                    </nz-progress>
                  }
                </td>
                <td>
                  <div class="report-actions">
                    @if (report.status === 'ready') {
                      <button 
                        nz-button 
                        nzType="text" 
                        nzSize="small"
                        (click)="downloadReport(report)"
                        nz-tooltip="Télécharger">
                        <nz-icon nzType="download"></nz-icon>
                      </button>
                    }
                    <button 
                      nz-button 
                      nzType="text" 
                      nzDanger
                      nzSize="small"
                      (click)="deleteReport(report.id)"
                      nz-tooltip="Supprimer">
                      <nz-icon nzType="delete"></nz-icon>
                    </button>
                  </div>
                </td>
              </tr>
            }
          </tbody>
        </nz-table>
      </div>
    } @else {
      <div class="empty-reports">
        <div class="empty-content">
          <div class="empty-icon">📄</div>
          <h4>Aucun rapport généré</h4>
          <p>Commencez par générer votre premier rapport en utilisant les templates ci-dessus.</p>
        </div>
      </div>
    }
  </div>

  <!-- Conseils et informations -->
  <div class="tips-card">
    <div class="tips-header">
      <h3 class="tips-title">💡 Conseils pour vos rapports</h3>
    </div>
    <div class="tips-grid">
      <div class="tip-item">
        <div class="tip-icon">⏰</div>
        <div class="tip-content">
          <h4>Programmation automatique</h4>
          <p>Programmez vos rapports pour qu'ils soient générés automatiquement chaque mois.</p>
        </div>
      </div>
      <div class="tip-item">
        <div class="tip-icon">📊</div>
        <div class="tip-content">
          <h4>Analyse comparative</h4>
          <p>Comparez vos performances sur différentes périodes pour identifier les tendances.</p>
        </div>
      </div>
      <div class="tip-item">
        <div class="tip-icon">📧</div>
        <div class="tip-content">
          <h4>Partage facilité</h4>
          <p>Exportez vos rapports en PDF ou Excel pour les partager avec votre équipe.</p>
        </div>
      </div>
    </div>
  </div>
</div>

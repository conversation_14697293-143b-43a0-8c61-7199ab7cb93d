<div class="dashboard-container">
  <!-- Header avec titre et actions rapides -->
  <div class="dashboard-header">
    <div class="header-content">
      <div class="welcome-section">
        <h1 class="dashboard-title">Tableau de bord</h1>
        <p class="dashboard-subtitle">Bienvenue dans votre espace de gestion Traiteria</p>
      </div>
      <div class="header-actions">
        <button 
          nz-button 
          nzType="default" 
          (click)="viewStatistics()"
          class="action-btn">
          <nz-icon nzType="bar-chart"></nz-icon>
          Statistiques
        </button>
        <button 
          nz-button 
          nzType="default" 
          (click)="viewReports()"
          class="action-btn">
          <nz-icon nzType="file-text"></nz-icon>
          Rapports
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques principales -->
  <div class="stats-grid">
    <nz-card class="stat-card revenue-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon revenue-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.8 10.9C9.53 10.31 8.8 9.7 8.8 8.75C8.8 7.66 9.81 6.9 11.5 6.9C13.28 6.9 13.94 7.75 14 9H16.21C16.14 7.28 15.09 5.7 13 5.19V3H10V5.16C8.06 5.58 6.5 6.84 6.5 8.77C6.5 11.08 8.41 12.23 11.2 12.9C13.7 13.5 14.2 14.38 14.2 15.31C14.2 16 13.71 17.1 11.5 17.1C9.44 17.1 8.63 16.18 8.5 15H6.32C6.44 17.19 8.08 18.42 10 18.83V21H13V18.85C14.95 18.5 16.5 17.35 16.5 15.3C16.5 12.46 14.07 11.5 11.8 10.9Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <nz-statistic
            [nzValue]="formatCurrency(stats.monthlyRevenue)"
            nzTitle="Chiffre d'affaires mensuel"
            class="stat-number">
          </nz-statistic>
          <div class="stat-trend positive">
            <nz-icon nzType="arrow-up"></nz-icon>
            +12% ce mois
          </div>
        </div>
      </div>
    </nz-card>

    <nz-card class="stat-card events-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon events-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19ZM7 10H12V15H7V10Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="stats.totalEvents" 
            nzTitle="Événements ce mois"
            class="stat-number">
          </nz-statistic>
          <div class="stat-trend positive">
            <nz-icon nzType="arrow-up"></nz-icon>
            +8 nouveaux
          </div>
        </div>
      </div>
    </nz-card>

    <nz-card class="stat-card clients-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon clients-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 4C18.2 4 20 5.8 20 8C20 10.2 18.2 12 16 12C13.8 12 12 10.2 12 8C12 5.8 13.8 4 16 4ZM16 14C20.4 14 24 15.8 24 18V20H8V18C8 15.8 11.6 14 16 14ZM8 4C10.2 4 12 5.8 12 8C12 10.2 10.2 12 8 12C5.8 12 4 10.2 4 8C4 5.8 5.8 4 8 4ZM8 14C12.4 14 16 15.8 16 18V20H0V18C0 15.8 3.6 14 8 14Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="stats.activeClients" 
            nzTitle="Clients actifs"
            class="stat-number">
          </nz-statistic>
          <div class="stat-trend positive">
            <nz-icon nzType="arrow-up"></nz-icon>
            +3 cette semaine
          </div>
        </div>
      </div>
    </nz-card>

    <nz-card class="stat-card completion-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon completion-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="stats.completionRate" 
            nzSuffix="%" 
            nzTitle="Taux de réussite"
            class="stat-number">
          </nz-statistic>
          <nz-progress 
            [nzPercent]="stats.completionRate" 
            nzSize="small" 
            nzStrokeColor="#34c759"
            class="completion-progress">
          </nz-progress>
        </div>
      </div>
    </nz-card>
  </div>

  <!-- Contenu principal -->
  <div class="main-content">
    <!-- Actions rapides -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">Actions rapides</h3>
        <p class="section-subtitle">Accédez rapidement aux fonctionnalités principales</p>
      </div>
      <div class="quick-actions-grid">
        @for (action of quickActions; track action.route) {
          <div
            class="quick-action-item"
            (click)="navigateToAction(action.route)">
            <div class="action-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                @if (action.icon === 'plus') {
                  <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill="currentColor"/>
                } @else if (action.icon === 'calendar') {
                  <path d="M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19Z" fill="currentColor"/>
                } @else if (action.icon === 'user-add') {
                  <path d="M15 12C17.21 12 19 10.21 19 8C19 5.79 17.21 4 15 4C12.79 4 11 5.79 11 8C11 10.21 12.79 12 15 12ZM6 10V7H4V10H1V12H4V15H6V12H9V10H6ZM15 14C12.33 14 7 15.34 7 18V20H23V18C23 15.34 17.67 14 15 14Z" fill="currentColor"/>
                } @else if (action.icon === 'file-add') {
                  <path d="M14 2H6C4.9 2 4.01 2.9 4.01 4L4 20C4 21.1 4.89 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20ZM11 15H13V12H16V10H13V7H11V10H8V12H11V15Z" fill="currentColor"/>
                }
              </svg>
            </div>
            <div class="action-content">
              <h4 class="action-title">{{ action.title }}</h4>
              <p class="action-description">{{ action.description }}</p>
            </div>
            <div class="action-arrow">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z" fill="currentColor"/>
              </svg>
            </div>
          </div>
        }
      </div>
    </div>

    <!-- Événements récents -->
    <div class="section">
      <div class="section-header">
        <div class="header-left">
          <h3 class="section-title">Événements récents</h3>
          <p class="section-subtitle">Derniers événements créés ou modifiés</p>
        </div>
        <button
          nz-button
          nzType="link"
          (click)="viewAllEvents()"
          class="view-all-btn">
          Voir tout
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z" fill="currentColor"/>
          </svg>
        </button>
      </div>

      <div class="events-list">
        @for (event of recentEvents; track event.id) {
          <div class="event-item">
            <div class="event-avatar">
              <div class="avatar-circle">{{ getClientInitials(event.client) }}</div>
            </div>
            <div class="event-info">
              <h4 class="event-title">{{ event.title }}</h4>
              <p class="event-client">{{ event.client }}</p>
              <div class="event-meta">
                <span class="event-date">{{ formatDate(event.date) }}</span>
                <span class="event-type">{{ event.type }}</span>
              </div>
            </div>
            <div class="event-status">
              <span class="status-badge" [class]="'status-' + event.status">
                {{ getStatusText(event.status) }}
              </span>
            </div>
            <div class="event-revenue">
              <span class="revenue-amount">{{ formatCurrency(event.revenue) }}</span>
            </div>
          </div>
        }
      </div>
    </div>
  </div>
</div>

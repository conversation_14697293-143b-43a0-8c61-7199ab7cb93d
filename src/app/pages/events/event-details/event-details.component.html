<div class="event-details-container">
  <!-- Breadcrumb -->
  <nz-breadcrumb class="breadcrumb">
    <nz-breadcrumb-item>
      <a routerLink="/events">
        <nz-icon nzType="calendar"></nz-icon>
        <span>Événements</span>
      </a>
    </nz-breadcrumb-item>
    <nz-breadcrumb-item>
      <span>{{ event?.type }}</span>
    </nz-breadcrumb-item>
  </nz-breadcrumb>

  <!-- Loading -->
  @if (loading) {
    <div class="loading-container">
      <nz-spin nzSize="large">
        <div class="loading-content">
          <p>Chargement des détails de l'événement...</p>
        </div>
      </nz-spin>
    </div>
  }

  <!-- Content -->
  @if (!loading && event) {
    <!-- Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="event-info">
          <div class="event-icon">
            <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
          </div>
          <div class="event-title">
            <h1>{{ event.type }}</h1>
            <p class="event-subtitle">{{ formatDate(event.date) }} • {{ event.heureDebut }} - {{ event.heureFin }}</p>
            <div class="event-tags">
              <nz-tag [nzColor]="getCategoryColor(event.category)">
                {{ getCategoryText(event.category) }}
              </nz-tag>
              <nz-tag [nzColor]="getStatusColor(event.status)">
                {{ getStatusText(event.status) }}
              </nz-tag>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <button nz-button nzType="default" (click)="goBack()" class="back-btn">
            <nz-icon nzType="arrow-left"></nz-icon>
            Retour
          </button>
          <button nz-button nzType="primary" (click)="editEvent()">
            <nz-icon nzType="edit"></nz-icon>
            Modifier
          </button>
        </div>
      </div>
    </div>

    <!-- Event Details Grid -->
    <div class="details-grid">
      <!-- Client Information -->
      <nz-card class="client-card" nzTitle="Informations client">
        <div class="client-info">
          <div class="client-avatar">
            <nz-icon nzType="user"></nz-icon>
          </div>
          <div class="client-details">
            <h3>{{ event.client.nom }} {{ event.client.prenom }}</h3>
            <div class="client-contact">
              <div class="contact-item">
                <nz-icon nzType="phone"></nz-icon>
                <a href="tel:{{ event.client.telephone }}">{{ event.client.telephone }}</a>
                <button nz-button nzType="link" nzSize="small" (click)="callClient()">
                  <nz-icon nzType="phone"></nz-icon>
                </button>
              </div>
              <div class="contact-item">
                <nz-icon nzType="mail"></nz-icon>
                <a href="mailto:{{ event.client.email }}">{{ event.client.email }}</a>
                <button nz-button nzType="link" nzSize="small" (click)="emailClient()">
                  <nz-icon nzType="mail"></nz-icon>
                </button>
              </div>
            </div>
            <button nz-button nzType="default" nzSize="small" (click)="goToClient()" class="view-client-btn">
              <nz-icon nzType="eye"></nz-icon>
              Voir le profil
            </button>
          </div>
        </div>
      </nz-card>

      <!-- Event Summary -->
      <nz-card class="summary-card" nzTitle="Résumé de l'événement">
        <div class="summary-stats">
          <div class="stat-item">
            <nz-icon nzType="team"></nz-icon>
            <div class="stat-content">
              <div class="stat-number">{{ event.personnes }}</div>
              <div class="stat-label">Invités</div>
            </div>
          </div>
          <div class="stat-item">
            <nz-icon nzType="table"></nz-icon>
            <div class="stat-content">
              <div class="stat-number">{{ event.nombreTables }}</div>
              <div class="stat-label">Tables</div>
            </div>
          </div>
          <div class="stat-item">
            <nz-icon nzType="dollar"></nz-icon>
            <div class="stat-content">
              <div class="stat-number">{{ formatCurrency(event.montant) }}</div>
              <div class="stat-label">Montant</div>
            </div>
          </div>
        </div>
      </nz-card>
    </div>

    <!-- Detailed Information -->
    <nz-card class="details-card" nzTitle="Détails de l'événement">
      <nz-descriptions nzBordered [nzColumn]="2">
        <nz-descriptions-item nzTitle="Lieu">
          <div class="location-info">
            <div><strong>{{ event.ville }}</strong></div>
            <div>{{ event.adresse }}</div>
          </div>
        </nz-descriptions-item>
        <nz-descriptions-item nzTitle="Configuration">
          {{ event.personnes }} personnes • {{ event.personnesParTable }} par table • {{ event.nombreTables }} tables
        </nz-descriptions-item>
        <nz-descriptions-item nzTitle="Services" [nzSpan]="2">
          <div class="services-list">
            @for (service of event.services; track service) {
              <nz-tag nzColor="blue">{{ service }}</nz-tag>
            }
          </div>
        </nz-descriptions-item>
        <nz-descriptions-item nzTitle="Menus" [nzSpan]="2">
          <div class="menus-list">
            @for (menu of event.menus; track menu) {
              <nz-tag nzColor="green">{{ menu }}</nz-tag>
            }
          </div>
        </nz-descriptions-item>
        @if (event.notes) {
          <nz-descriptions-item nzTitle="Notes" [nzSpan]="2">
            <div class="notes-content">{{ event.notes }}</div>
          </nz-descriptions-item>
        }
      </nz-descriptions>
    </nz-card>

    <!-- Materials -->
    <nz-card class="materials-card" nzTitle="Matériels nécessaires">
      @if (event.materiels.length > 0) {
        <nz-table [nzData]="event.materiels" [nzShowPagination]="false" nzBordered class="materials-table">
          <thead>
            <tr>
              <th>Matériel</th>
              <th>Quantité</th>
              <th>Unité</th>
            </tr>
          </thead>
          <tbody>
            @for (materiel of event.materiels; track materiel.nom) {
              <tr>
                <td>
                  <strong>{{ materiel.nom }}</strong>
                </td>
                <td>{{ materiel.quantite }}</td>
                <td>{{ materiel.unite }}</td>
              </tr>
            }
          </tbody>
        </nz-table>
      } @else {
        <nz-empty
          nzNotFoundImage="simple"
          nzNotFoundContent="Aucun matériel spécifié">
        </nz-empty>
      }
    </nz-card>

    <!-- Timeline -->
    <nz-card class="timeline-card" nzTitle="Chronologie">
      <nz-timeline>
        <nz-timeline-item nzColor="green">
          <p>Événement créé</p>
          <p class="timeline-date">{{ formatDate(event.dateCreation) }}</p>
        </nz-timeline-item>
        @if (event.status === 'confirmé') {
          <nz-timeline-item nzColor="blue">
            <p>Événement confirmé</p>
            <p class="timeline-date">En attente de réalisation</p>
          </nz-timeline-item>
        }
        @if (event.status === 'terminé') {
          <nz-timeline-item nzColor="green">
            <p>Événement terminé</p>
            <p class="timeline-date">{{ formatDate(event.date) }}</p>
          </nz-timeline-item>
        }
        @if (event.status === 'annulé') {
          <nz-timeline-item nzColor="red">
            <p>Événement annulé</p>
            <p class="timeline-date">Annulation confirmée</p>
          </nz-timeline-item>
        }
      </nz-timeline>
    </nz-card>
  }

  <!-- Error state -->
  @if (!loading && !event) {
    <nz-card class="error-card">
      <nz-empty
        nzNotFoundImage="simple"
        nzNotFoundContent="Événement introuvable">
        <div nz-empty-footer>
          <button nz-button nzType="primary" (click)="goBack()">
            <nz-icon nzType="arrow-left"></nz-icon>
            Retour à la liste
          </button>
        </div>
      </nz-empty>
    </nz-card>
  }
</div>

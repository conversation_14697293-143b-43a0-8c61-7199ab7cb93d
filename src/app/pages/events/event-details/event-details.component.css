/* Container principal */
.event-details-container {
  padding: 24px;
  background: var(--apple-gray-50);
  min-height: 100vh;
}

/* Breadcrumb */
.breadcrumb {
  margin-bottom: 24px;
  padding: 12px 16px;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.breadcrumb a {
  color: var(--apple-grenat);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb a:hover {
  color: var(--apple-grenat-dark);
}

/* Loading */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  text-align: center;
  color: var(--apple-gray-600);
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  background: var(--gradient-primary);
}

.event-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.event-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--apple-grenat-pale);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: var(--apple-grenat);
  box-shadow: var(--shadow-md);
}

.event-title h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
}

.event-subtitle {
  margin: 4px 0 8px 0;
  color: var(--apple-gray-600);
  font-size: 14px;
}

.event-tags {
  display: flex;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.back-btn {
  border-color: var(--apple-gray-300);
  color: var(--apple-gray-700);
}

.back-btn:hover {
  border-color: var(--apple-grenat);
  color: var(--apple-grenat);
}

/* Details Grid */
.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

/* Cards */
.client-card,
.summary-card,
.details-card,
.materials-card,
.timeline-card,
.error-card {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--apple-gray-200);
}

.client-card .ant-card-head,
.summary-card .ant-card-head,
.details-card .ant-card-head,
.materials-card .ant-card-head,
.timeline-card .ant-card-head {
  background: var(--gradient-secondary);
  border-bottom: 1px solid var(--apple-gray-200);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.client-card .ant-card-head-title,
.summary-card .ant-card-head-title,
.details-card .ant-card-head-title,
.materials-card .ant-card-head-title,
.timeline-card .ant-card-head-title {
  color: var(--apple-black);
  font-weight: 600;
}

/* Client Card */
.client-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.client-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--apple-grenat-pale);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--apple-grenat);
}

.client-details h3 {
  margin: 0 0 8px 0;
  color: var(--apple-black);
  font-size: 18px;
}

.client-contact {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.contact-item a {
  color: var(--apple-grenat);
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

.view-client-btn {
  border-color: var(--apple-grenat);
  color: var(--apple-grenat);
}

.view-client-btn:hover {
  background: var(--apple-grenat);
  color: white;
}

/* Summary Card */
.summary-stats {
  display: flex;
  justify-content: space-around;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--gradient-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--apple-gray-200);
  flex: 1;
  text-align: center;
  justify-content: center;
}

.stat-item nz-icon {
  font-size: 24px;
  color: var(--apple-grenat);
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: var(--apple-black);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: var(--apple-gray-600);
  margin-top: 4px;
}

/* Descriptions */
.ant-descriptions-item-label {
  font-weight: 600;
  color: var(--apple-gray-700);
}

.ant-descriptions-item-content {
  color: var(--apple-black);
}

.location-info div:first-child {
  margin-bottom: 4px;
}

.services-list,
.menus-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.notes-content {
  background: var(--apple-gray-50);
  padding: 12px;
  border-radius: var(--radius-md);
  border-left: 4px solid var(--apple-grenat);
  font-style: italic;
}

/* Materials Table */
.materials-table {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.materials-table .ant-table-thead > tr > th {
  background: var(--apple-gray-50);
  border-bottom: 2px solid var(--apple-gray-200);
  font-weight: 600;
  color: var(--apple-gray-700);
}

.materials-table .ant-table-tbody > tr:hover > td {
  background: var(--apple-grenat-pale) !important;
}

/* Timeline */
.timeline-date {
  color: var(--apple-gray-600);
  font-size: 12px;
  margin: 4px 0 0 0;
}

.ant-timeline-item-content p:first-child {
  font-weight: 600;
  color: var(--apple-black);
  margin-bottom: 4px;
}

/* Tags */
.ant-tag {
  border-radius: var(--radius-md);
  font-weight: 500;
}

/* Empty states */
.ant-empty {
  padding: 40px 20px;
}

.ant-empty-footer {
  margin-top: 16px;
}

/* Responsive */
@media (max-width: 1024px) {
  .details-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .event-details-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .event-info {
    flex-direction: column;
    text-align: center;
  }

  .summary-stats {
    flex-direction: column;
  }

  .client-info {
    flex-direction: column;
    text-align: center;
  }

  .event-title h1 {
    font-size: 24px;
  }

  .materials-table {
    font-size: 12px;
  }
}

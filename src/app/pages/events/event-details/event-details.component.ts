import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzSpinModule } from 'ng-zorro-antd/spin';

import { NzTimelineModule } from 'ng-zorro-antd/timeline';

interface Event {
  id: number;
  type: string;
  category: string;
  client: {
    id: string;
    nom: string;
    prenom: string;
    telephone: string;
    email: string;
  };
  date: Date;
  heureDebut: string;
  heureFin: string;
  personnes: number;
  personnesParTable: number;
  nombreTables: number;
  ville: string;
  adresse: string;
  montant: number;
  status: string;
  theme?: string;
  services: string[];
  menus: string[];
  materiels: Array<{
    nom: string;
    quantite: number;
    unite: string;
  }>;
  notes?: string;
  menuNotes?: string;
  serviceNotes?: string;
  dateCreation: Date;
}

@Component({
  selector: 'app-event-details',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzTagModule,
    NzDividerModule,
    NzDescriptionsModule,
    NzTableModule,
    NzEmptyModule,
    NzSpinModule,

    NzTimelineModule
  ],
  templateUrl: './event-details.component.html',
  styleUrl: './event-details.component.css'
})
export class EventDetailsComponent implements OnInit {
  eventId: string | null = null;
  event: Event | null = null;
  loading = true;

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.eventId = this.route.snapshot.paramMap.get('id');
    if (this.eventId) {
      this.loadEventDetails();
    } else {
      this.router.navigate(['/events']);
    }
  }

  private loadEventDetails(): void {
    this.loading = true;
    
    // Simulation d'un appel API
    setTimeout(() => {
      // Données simulées - à remplacer par un vrai service
      this.event = {
        id: parseInt(this.eventId!),
        type: 'Mariage',
        category: 'particulier',
        client: {
          id: '1',
          nom: 'Alami',
          prenom: 'Fatima',
          telephone: '0612345678',
          email: '<EMAIL>'
        },
        date: new Date('2024-06-15'),
        heureDebut: '18:00',
        heureFin: '02:00',
        personnes: 150,
        personnesParTable: 8,
        nombreTables: 19,
        ville: 'Casablanca',
        adresse: 'Salle des fêtes Al Andalous, Boulevard Zerktouni, Casablanca',
        montant: 45000,
        status: 'confirmé',
        theme: 'Mariage traditionnel marocain',
        services: ['Traiteur', 'Décoration', 'Animation', 'Photographie'],
        menus: ['Menu Marocain Traditionnel', 'Buffet International'],
        materiels: [
          { nom: 'Tables rondes', quantite: 19, unite: 'unité' },
          { nom: 'Chaises', quantite: 150, unite: 'unité' },
          { nom: 'Nappes blanches', quantite: 19, unite: 'unité' },
          { nom: 'Centres de table', quantite: 19, unite: 'unité' },
          { nom: 'Système son', quantite: 1, unite: 'ensemble' },
          { nom: 'Éclairage', quantite: 1, unite: 'ensemble' }
        ],
        notes: 'Prévoir espace pour orchestre andalou. Décoration en blanc et doré.',
        menuNotes: 'Pas de porc. Prévoir options végétariennes. Service à 20h précises.',
        serviceNotes: 'Photographe doit arriver 1h avant. Animation jusqu\'à 2h du matin.',
        dateCreation: new Date('2024-01-15')
      };

      this.loading = false;
    }, 1000);
  }

  getStatusColor(status: string): string {
    const colors: { [key: string]: string } = {
      'confirmé': 'green',
      'en_attente': 'orange',
      'terminé': 'blue',
      'annulé': 'red'
    };
    return colors[status] || 'default';
  }

  getStatusText(status: string): string {
    const texts: { [key: string]: string } = {
      'confirmé': 'Confirmé',
      'en_attente': 'En attente',
      'terminé': 'Terminé',
      'annulé': 'Annulé'
    };
    return texts[status] || status;
  }

  getCategoryColor(category: string): string {
    return category === 'particulier' ? 'blue' : 'purple';
  }

  getCategoryText(category: string): string {
    return category === 'particulier' ? 'Particulier' : 'Entreprise';
  }

  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('fr-FR', {
      weekday: 'long',
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    }).format(date);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount);
  }

  editEvent(): void {
    this.router.navigate(['/events/edit', this.eventId]);
  }

  goToClient(): void {
    if (this.event?.client.id) {
      this.router.navigate(['/clients/details', this.event.client.id]);
    }
  }

  goBack(): void {
    this.router.navigate(['/events']);
  }

  callClient(): void {
    if (this.event?.client.telephone) {
      window.open(`tel:${this.event.client.telephone}`);
    }
  }

  emailClient(): void {
    if (this.event?.client.email) {
      window.open(`mailto:${this.event.client.email}`);
    }
  }
}

import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import {NzTooltipDirective} from 'ng-zorro-antd/tooltip';

interface EventItem {
  id: number;
  type: string;
  category: 'particulier' | 'entreprise';
  client: string;
  date: Date;
  status: 'confirmed' | 'pending' | 'cancelled';
  nbPersonnes: number;
  ville: string;
  montant: number;
  checked?: boolean;
}

@Component({
  selector: 'app-events-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    NzButtonModule,
    NzCardModule,
    NzIconModule,
    NzTableModule,
    NzTagModule,
    NzInputModule,
    NzSelectModule,
    NzDatePickerModule,
    NzModalModule,
    NzDropDownModule,
    NzMenuModule,
    NzCheckboxModule
  ],
  templateUrl: './events-list.component.html',
  styleUrl: './events-list.component.css'
})
export class EventsListComponent {
  constructor(
    private message: NzMessageService,
    private modal: NzModalService
  ) {}
  // Filtres
  searchText = '';
  selectedCategory = '';
  selectedStatus = '';
  selectedDateRange: Date[] = [];

  // Sélection
  allChecked = false;
  indeterminate = false;

  // Fonctions de tri
  sortByType = (a: EventItem, b: EventItem) => a.type.localeCompare(b.type);
  sortByCategory = (a: EventItem, b: EventItem) => a.category.localeCompare(b.category);
  sortByClient = (a: EventItem, b: EventItem) => a.client.localeCompare(b.client);
  sortByDate = (a: EventItem, b: EventItem) => a.date.getTime() - b.date.getTime();
  sortByPersonnes = (a: EventItem, b: EventItem) => a.nbPersonnes - b.nbPersonnes;
  sortByVille = (a: EventItem, b: EventItem) => a.ville.localeCompare(b.ville);
  sortByMontant = (a: EventItem, b: EventItem) => a.montant - b.montant;
  sortByStatus = (a: EventItem, b: EventItem) => a.status.localeCompare(b.status);

  // Données
  events: EventItem[] = [
    {
      id: 1,
      type: 'Mariage',
      category: 'particulier',
      client: 'Ahmed Alami',
      date: new Date('2024-07-15T18:30:00'),
      status: 'confirmed',
      nbPersonnes: 150,
      ville: 'Casablanca',
      montant: 25000,
      checked: false
    },
    {
      id: 2,
      type: 'Séminaire',
      category: 'entreprise',
      client: 'Entreprise XYZ',
      date: new Date('2024-07-20T14:00:00'),
      status: 'pending',
      nbPersonnes: 80,
      ville: 'Rabat',
      montant: 15000,
      checked: false
    },
    {
      id: 3,
      type: 'Anniversaire',
      category: 'particulier',
      client: 'Fatima Benali',
      date: new Date('2024-08-05T20:00:00'),
      status: 'confirmed',
      nbPersonnes: 50,
      ville: 'Marrakech',
      montant: 8000,
      checked: false
    },
    {
      id: 4,
      type: 'Fête de travail',
      category: 'entreprise',
      client: 'Société ABC',
      date: new Date('2024-08-12'),
      status: 'cancelled',
      nbPersonnes: 120,
      ville: 'Fès',
      montant: 18000,
      checked: false
    }
  ];

  constructor(private message: NzMessageService) {}

  get filteredEvents(): EventItem[] {
    return this.events.filter(event => {
      const matchesSearch = !this.searchText ||
        event.client.toLowerCase().includes(this.searchText.toLowerCase()) ||
        event.type.toLowerCase().includes(this.searchText.toLowerCase()) ||
        event.ville.toLowerCase().includes(this.searchText.toLowerCase());

      const matchesCategory = !this.selectedCategory || event.category === this.selectedCategory;
      const matchesStatus = !this.selectedStatus || event.status === this.selectedStatus;

      const matchesDate = !this.selectedDateRange.length ||
        (this.selectedDateRange[0] && this.selectedDateRange[1] &&
         event.date >= this.selectedDateRange[0] && event.date <= this.selectedDateRange[1]);

      return matchesSearch && matchesCategory && matchesStatus && matchesDate;
    });
  }

  get selectedEvents(): EventItem[] {
    return this.events.filter(event => event.checked);
  }

  get totalAmount(): string {
    const total = this.filteredEvents.reduce((sum, event) => sum + event.montant, 0);
    return total.toLocaleString('fr-FR');
  }

  // Gestion des checkboxes
  updateAllChecked(): void {
    this.indeterminate = false;
    if (this.allChecked) {
      this.filteredEvents.forEach(event => event.checked = true);
    } else {
      this.filteredEvents.forEach(event => event.checked = false);
    }
    this.updateSingleChecked();
  }

  updateSingleChecked(): void {
    const filteredEvents = this.filteredEvents;
    if (filteredEvents.every(event => event.checked)) {
      this.allChecked = true;
      this.indeterminate = false;
    } else if (filteredEvents.some(event => event.checked)) {
      this.allChecked = false;
      this.indeterminate = true;
    } else {
      this.allChecked = false;
      this.indeterminate = false;
    }
  }

  // Actions avec modales
  deleteEvent(id: number): void {
    const event = this.events.find(e => e.id === id);
    if (!event) return;

    this.modal.confirm({
      nzTitle: 'Confirmer la suppression',
      nzContent: `Êtes-vous sûr de vouloir supprimer l'événement "<strong>${event.title}</strong>" ?<br><br>Cette action est irréversible.`,
      nzOkText: 'Supprimer',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        this.events = this.events.filter(e => e.id !== id);
        this.message.success(`Événement "${event.title}" supprimé avec succès !`);
      }
    });
  }

  deleteSelectedEvents(): void {
    const count = this.selectedEvents.length;
    this.modal.confirm({
      nzTitle: 'Confirmer la suppression',
      nzContent: `Êtes-vous sûr de vouloir supprimer <strong>${count}</strong> événement(s) ?<br><br>Cette action est irréversible.`,
      nzOkText: 'Supprimer',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        const selectedIds = this.selectedEvents.map(event => event.id);
        this.events = this.events.filter(event => !selectedIds.includes(event.id));
        this.message.success(`${count} événement(s) supprimé(s) avec succès !`);
        this.allChecked = false;
        this.indeterminate = false;
      }
    });
  }

  clearFilters(): void {
    this.searchText = '';
    this.selectedCategory = '';
    this.selectedStatus = '';
    this.selectedDateRange = [];
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'confirmed': return 'green';
      case 'pending': return 'orange';
      case 'cancelled': return 'red';
      default: return 'default';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'confirmed': return 'Confirmé';
      case 'pending': return 'En attente';
      case 'cancelled': return 'Annulé';
      default: return status;
    }
  }


}

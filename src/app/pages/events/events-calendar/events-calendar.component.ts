import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

// FullCalendar imports
import { FullCalendarModule } from '@fullcalendar/angular';
import { CalendarOptions, EventClickArg, DateSelectArg } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';

// Ng-Zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-events-calendar',
  standalone: true,
  imports: [
    CommonModule,
    FullCalendarModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzToolTipModule
  ],
  templateUrl: './events-calendar.component.html',
  styleUrl: './events-calendar.component.css'
})
export class EventsCalendarComponent implements OnInit {
  
  calendarOptions: CalendarOptions = {
    initialView: 'dayGridMonth',
    plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin],
    headerToolbar: {
      left: 'prev,next today',
      center: 'title',
      right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
    },
    locale: 'fr',
    firstDay: 1, // Lundi
    weekends: true,
    editable: true,
    selectable: true,
    selectMirror: true,
    dayMaxEvents: true,
    height: 'auto',
    events: [],
    select: this.handleDateSelect.bind(this),
    eventClick: this.handleEventClick.bind(this),
    eventsSet: this.handleEvents.bind(this),
    eventDisplay: 'block',
    displayEventTime: true,
    eventTimeFormat: {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    },
    slotLabelFormat: {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    },
    buttonText: {
      today: "Aujourd'hui",
      month: 'Mois',
      week: 'Semaine',
      day: 'Jour',
      list: 'Liste'
    },
    moreLinkText: 'événement(s)',
    noEventsText: 'Aucun événement à afficher'
  };

  currentEvents: any[] = [];

  constructor(
    private router: Router,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.loadEvents();
  }

  loadEvents(): void {
    // Simulation de données d'événements
    const mockEvents = [
      {
        id: '1',
        title: 'Mariage Dupont',
        start: '2024-01-15T14:00:00',
        end: '2024-01-15T23:00:00',
        backgroundColor: '#8B1538',
        borderColor: '#8B1538',
        textColor: '#ffffff',
        extendedProps: {
          client: 'M. et Mme Dupont',
          type: 'Mariage',
          category: 'particulier',
          nbPersonnes: 120,
          ville: 'Casablanca'
        }
      },
      {
        id: '2',
        title: 'Déjeuner Delrus',
        start: '2024-01-16T12:00:00',
        end: '2024-01-16T15:00:00',
        backgroundColor: '#34c759',
        borderColor: '#34c759',
        textColor: '#ffffff',
        extendedProps: {
          client: 'Entreprise Delrus',
          type: 'Repas séminaire',
          category: 'entreprise',
          nbPersonnes: 50,
          ville: 'Rabat'
        }
      },
      {
        id: '3',
        title: 'Cocktail Martin',
        start: '2024-01-17T18:00:00',
        end: '2024-01-17T22:00:00',
        backgroundColor: '#ff9500',
        borderColor: '#ff9500',
        textColor: '#ffffff',
        extendedProps: {
          client: 'Martin & Associés',
          type: 'Cocktail',
          category: 'entreprise',
          nbPersonnes: 80,
          ville: 'Marrakech'
        }
      },
      {
        id: '4',
        title: 'Anniversaire Ben Ali',
        start: '2024-01-20T16:00:00',
        end: '2024-01-20T21:00:00',
        backgroundColor: '#af52de',
        borderColor: '#af52de',
        textColor: '#ffffff',
        extendedProps: {
          client: 'Famille Ben Ali',
          type: 'Anniversaire',
          category: 'particulier',
          nbPersonnes: 60,
          ville: 'Fès'
        }
      },
      {
        id: '5',
        title: 'Séminaire TechCorp',
        start: '2024-01-22T09:00:00',
        end: '2024-01-22T17:00:00',
        backgroundColor: '#007aff',
        borderColor: '#007aff',
        textColor: '#ffffff',
        extendedProps: {
          client: 'TechCorp',
          type: 'Séminaire',
          category: 'entreprise',
          nbPersonnes: 100,
          ville: 'Casablanca'
        }
      }
    ];

    this.calendarOptions = {
      ...this.calendarOptions,
      events: mockEvents
    };
  }

  handleDateSelect(selectInfo: DateSelectArg): void {
    // Rediriger vers la création d'événement avec la date et heure pré-sélectionnées
    const selectedDate = selectInfo.start.toISOString().split('T')[0];
    const selectedTime = selectInfo.start.toTimeString().substring(0, 5); // Format HH:MM

    this.router.navigate(['/events/new'], {
      queryParams: {
        date: selectedDate,
        time: selectedTime
      }
    });
  }

  handleEventClick(clickInfo: EventClickArg): void {
    const eventId = clickInfo.event.id;
    this.router.navigate(['/events/edit', eventId]);
  }

  handleEvents(events: any[]): void {
    this.currentEvents = events;
  }

  createNewEvent(): void {
    this.router.navigate(['/events/new']);
  }

  goToEventsList(): void {
    this.router.navigate(['/events']);
  }

  refreshCalendar(): void {
    this.loadEvents();
    this.message.success('Calendrier actualisé !');
  }
}

import { Routes } from '@angular/router';

export const EVENTS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./events-list/events-list.component').then(m => m.EventsListComponent),
    data: { breadcrumb: { label: 'Liste des événements' } }
  },
  {
    path: 'new',
    loadComponent: () => import('../../components/event-form/event-form.component').then(m => m.EventFormComponent),
    data: { breadcrumb: { label: 'Nouvel événement', icon: 'plus' } }
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('../../components/event-form/event-form.component').then(m => m.EventFormComponent),
    data: { breadcrumb: { label: 'Modifier événement', icon: 'edit' } }
  },
  {
    path: 'calendar',
    loadComponent: () => import('./events-calendar/events-calendar.component').then(m => m.EventsCalendarComponent),
    data: { breadcrumb: { label: 'Calendrier des événements' } }
  }
];

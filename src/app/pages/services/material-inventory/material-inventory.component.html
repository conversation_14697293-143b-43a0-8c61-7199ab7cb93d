<div class="inventory-container">
  <!-- Header avec titre et actions -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">Inventaire du matériel de location</h2>
      <div class="header-actions">
        <button 
          nz-button 
          nzType="default" 
          (click)="resetInventory()"
          [nzLoading]="isLoading"
          nz-tooltip="Réinitialiser l'inventaire">
          <nz-icon nzType="reload"></nz-icon>
          Réinitialiser
        </button>
        <button 
          nz-button 
          nzType="dashed" 
          (click)="addNewMaterial()"
          class="add-btn">
          <nz-icon nzType="plus"></nz-icon>
          Ajouter matériel
        </button>
        <button 
          nz-button 
          nzType="primary" 
          (click)="saveInventory()"
          [nzLoading]="isLoading"
          class="save-btn">
          <nz-icon nzType="save"></nz-icon>
          <PERSON><PERSON><PERSON><PERSON>
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-section">
    <div class="stat-card">
      <div class="stat-content">
        <div class="stat-icon types-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H4.99C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 15H15C15 16.66 13.65 18 12 18C10.35 18 9 16.66 9 15H5V5H19V15Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ materials.length }}</div>
          <div class="stat-label">Types de matériel</div>
        </div>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-content">
        <div class="stat-icon total-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM9 17H7V10H9V17ZM13 17H11V7H13V17ZM17 17H15V13H17V17Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ getTotalItems() }}</div>
          <div class="stat-label">Articles au total</div>
        </div>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-content">
        <div class="stat-icon categories-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 6H6V8H4V6ZM4 10H6V12H4V10ZM4 14H6V16H4V14ZM8 6H20V8H8V6ZM8 10H20V12H8V10ZM8 14H20V16H8V14Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ getCategoriesCount() }}</div>
          <div class="stat-label">Catégories</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Grille des matériaux -->
  <div class="materials-grid">
    @for (material of materials; track material.id) {
      <div class="material-card" [class.loading]="isLoading">
        <div class="material-header">
          <div class="material-icon" [class]="'icon-' + material.id">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              @if (material.id === '1') {
                <!-- Chapiteaux -->
                <path d="M12 3L2 12H4L12 5L20 12H22L12 3ZM5 14V20H19V14H17V18H7V14H5Z" fill="currentColor"/>
              } @else if (material.id === '2') {
                <!-- Estrade -->
                <path d="M3 18H21V20H3V18ZM5 16H19V18H5V16ZM7 14H17V16H7V14ZM9 12H15V14H9V12Z" fill="currentColor"/>
              } @else if (material.id === '3') {
                <!-- Dalo -->
                <path d="M4 4H20V6H4V4ZM6 8H18V10H6V8ZM8 12H16V14H8V12ZM10 16H14V18H10V16ZM12 20H12V22H12V20Z" fill="currentColor"/>
              } @else if (material.id === '4') {
                <!-- Scène -->
                <path d="M2 17H22V19H2V17ZM4 15H20V17H4V15ZM6 13H18V15H6V13ZM8 11H16V13H8V11ZM10 9H14V11H10V9Z" fill="currentColor"/>
              } @else if (material.id === '5') {
                <!-- Salle de fête -->
                <path d="M12 2L2 7V17H22V7L12 2ZM4 15V9L12 5L20 9V15H4Z" fill="currentColor"/>
              } @else if (material.id === '6') {
                <!-- Tables rondes -->
                <path d="M12 2C17.52 2 22 6.48 22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2ZM12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12C20 7.58 16.42 4 12 4Z" fill="currentColor"/>
              } @else if (material.id === '7') {
                <!-- Chaises -->
                <path d="M7 11V7C7 5.9 7.9 5 9 5H15C16.1 5 17 5.9 17 7V11H19V7C19 4.79 17.21 3 15 3H9C6.79 3 5 4.79 5 7V11H7ZM5 13V21H7V19H17V21H19V13H5Z" fill="currentColor"/>
              } @else if (material.id === '8') {
                <!-- Éclairage LED -->
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 6.5V7.5C15 8.3 14.3 9 13.5 9S12 8.3 12 7.5V6.5L6 7V9C6 10 7 11 8 11V12.5C7.2 12.9 6.5 13.7 6.5 15V16.5C6.5 17.6 7.4 18.5 8.5 18.5S10.5 17.6 10.5 16.5V15C10.5 13.7 9.8 12.9 9 12.5V11C10 11 11 10 11 9H13C13 10 14 11 15 11V12.5C14.2 12.9 13.5 13.7 13.5 15V16.5C13.5 17.6 14.4 18.5 15.5 18.5S17.5 17.6 17.5 16.5V15C17.5 13.7 16.8 12.9 16 12.5V11C17 11 18 10 18 9Z" fill="currentColor"/>
              } @else {
                <!-- Icône par défaut - Matériel générique -->
                <path d="M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
              }
            </svg>
          </div>
          <div class="material-info">
            <h3 class="material-name">{{ material.name }}</h3>
            <p class="material-description">{{ material.description }}</p>
            <span class="material-category">{{ material.category }}</span>
          </div>
          <div class="material-actions">
            <button 
              nz-button 
              nzType="text" 
              nzDanger
              nzSize="small"
              (click)="deleteMaterial(material)"
              nz-tooltip="Supprimer ce matériel"
              class="delete-btn">
              <nz-icon nzType="delete"></nz-icon>
            </button>
          </div>
        </div>
        
        <div class="quantity-section">
          <div class="quantity-label">
            <span>Quantité disponible</span>
            <span class="unit-label">{{ material.unit }}</span>
          </div>
          <div class="quantity-controls">
            <nz-input-number
              [(ngModel)]="material.quantity"
              [nzMin]="0"
              [nzMax]="9999"
              [nzStep]="1"
              (ngModelChange)="updateQuantity(material, $event)"
              class="quantity-input">
            </nz-input-number>
          </div>
        </div>
        
        <div class="availability-indicator">
          @if (material.quantity === 0) {
            <div class="status-badge unavailable">
              <nz-icon nzType="close-circle"></nz-icon>
              Non disponible
            </div>
          } @else if (material.quantity <= 2) {
            <div class="status-badge low-stock">
              <nz-icon nzType="exclamation-circle"></nz-icon>
              Stock faible
            </div>
          } @else {
            <div class="status-badge available">
              <nz-icon nzType="check-circle"></nz-icon>
              Disponible
            </div>
          }
        </div>
      </div>
    }
  </div>

  <!-- Message si aucun matériel -->
  @if (materials.length === 0) {
    <div class="empty-state">
      <div class="empty-content">
        <div class="empty-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H4.99C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 15H15C15 16.66 13.65 18 12 18C10.35 18 9 16.66 9 15H5V5H19V15Z" fill="currentColor"/>
          </svg>
        </div>
        <h3>Aucun matériel dans l'inventaire</h3>
        <p>Commencez par ajouter du matériel de location à votre inventaire.</p>
        <button
          nz-button
          nzType="primary"
          (click)="addNewMaterial()"
          class="add-first-btn">
          <nz-icon nzType="plus"></nz-icon>
          Ajouter le premier matériel
        </button>
      </div>
    </div>
  }
</div>

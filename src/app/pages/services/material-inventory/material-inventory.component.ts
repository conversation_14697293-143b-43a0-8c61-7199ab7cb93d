import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// Ng-Zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { AddMaterialModalComponent } from './add-material-modal/add-material-modal.component';

interface MaterialItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  unit: string;
  category: string;
}

@Component({
  selector: 'app-material-inventory',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzInputNumberModule,
    NzButtonModule,
    NzIconModule,
    NzModalModule,
    NzInputModule,
    NzToolTipModule
  ],
  templateUrl: './material-inventory.component.html',
  styleUrl: './material-inventory.component.css'
})
export class MaterialInventoryComponent implements OnInit {

  materials: MaterialItem[] = [
    {
      id: '1',
      name: 'Chapiteaux',
      description: 'Tentes de réception pour événements extérieurs',
      quantity: 5,
      unit: 'unité(s)',
      category: 'Structure'
    },
    {
      id: '2',
      name: 'Estrade',
      description: 'Plateformes surélevées pour spectacles',
      quantity: 3,
      unit: 'unité(s)',
      category: 'Structure'
    },
    {
      id: '3',
      name: 'Dalo',
      description: 'Structures de décoration traditionnelle',
      quantity: 8,
      unit: 'unité(s)',
      category: 'Décoration'
    },
    {
      id: '4',
      name: 'Scène',
      description: 'Scènes modulaires pour performances',
      quantity: 2,
      unit: 'unité(s)',
      category: 'Structure'
    },
    {
      id: '5',
      name: 'Salle de fête',
      description: 'Espaces de réception couverts',
      quantity: 1,
      unit: 'unité(s)',
      category: 'Espace'
    },
    {
      id: '6',
      name: 'Tables rondes',
      description: 'Tables de 8 personnes',
      quantity: 50,
      unit: 'unité(s)',
      category: 'Mobilier'
    },
    {
      id: '7',
      name: 'Chaises',
      description: 'Chaises de réception élégantes',
      quantity: 400,
      unit: 'unité(s)',
      category: 'Mobilier'
    },
    {
      id: '8',
      name: 'Éclairage LED',
      description: 'Systèmes d\'éclairage professionnel',
      quantity: 15,
      unit: 'kit(s)',
      category: 'Technique'
    }
  ];

  isLoading = false;

  constructor(
    private message: NzMessageService,
    private modal: NzModalService
  ) {}

  ngOnInit(): void {
    this.loadMaterials();
  }

  loadMaterials(): void {
    this.isLoading = true;

    // Simulation de chargement
    setTimeout(() => {
      this.isLoading = false;
    }, 500);
  }

  updateQuantity(material: MaterialItem, newQuantity: number): void {
    if (newQuantity >= 0) {
      material.quantity = newQuantity;
      this.message.success(`Quantité mise à jour : ${material.name} (${newQuantity} ${material.unit})`);
    }
  }

  addNewMaterial(): void {
    const modalRef = this.modal.create({
      nzTitle: 'Ajouter un nouveau matériel',
      nzContent: AddMaterialModalComponent,
      nzOkText: 'Ajouter',
      nzCancelText: 'Annuler',
      nzWidth: 500,
      nzOnOk: () => {
        const component = modalRef.getContentComponent();
        if (component && component.isValid()) {
          component.onSubmit();
          return true;
        } else {
          this.message.error('Veuillez saisir un nom de matériel valide');
          return false;
        }
      }
    });

    // Écouter l'événement d'ajout de matériel
    const component = modalRef.getContentComponent();
    if (component) {
      component.materialAdded.subscribe((newMaterialData: any) => {
        const newMaterial: MaterialItem = {
          id: Date.now().toString(),
          name: newMaterialData.name,
          description: newMaterialData.description || 'Nouveau matériel',
          quantity: newMaterialData.quantity,
          unit: newMaterialData.unit,
          category: 'Autre'
        };

        this.materials.push(newMaterial);
        this.message.success(`Matériel "${newMaterial.name}" ajouté avec succès !`);
        modalRef.close();
      });
    }
  }

  deleteMaterial(material: MaterialItem): void {
    this.modal.confirm({
      nzTitle: 'Confirmer la suppression',
      nzContent: `Êtes-vous sûr de vouloir supprimer "<strong>${material.name}</strong>" de l'inventaire ?<br><br>Cette action est irréversible.`,
      nzOkText: 'Supprimer',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        const index = this.materials.findIndex(m => m.id === material.id);
        if (index > -1) {
          this.materials.splice(index, 1);
          this.message.success(`Matériel "${material.name}" supprimé !`);
        }
      }
    });
  }

  saveInventory(): void {
    this.isLoading = true;

    // Simulation de sauvegarde
    setTimeout(() => {
      this.message.success('Inventaire sauvegardé avec succès !');
      this.isLoading = false;
      console.log('Inventaire sauvegardé:', this.materials);
    }, 1000);
  }

  resetInventory(): void {
    this.modal.confirm({
      nzTitle: 'Réinitialiser l\'inventaire',
      nzContent: 'Êtes-vous sûr de vouloir réinitialiser tout l\'inventaire aux valeurs par défaut ?',
      nzOkText: 'Réinitialiser',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        this.loadMaterials();
        this.message.info('Inventaire réinitialisé');
      }
    });
  }

  getTotalItems(): number {
    return this.materials.reduce((total, material) => total + material.quantity, 0);
  }

  getCategoriesCount(): number {
    const categories = new Set(this.materials.map(m => m.category));
    return categories.size;
  }
}

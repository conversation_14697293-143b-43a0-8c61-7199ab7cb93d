import { Routes } from '@angular/router';

export const SERVICES_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'particulier',
    pathMatch: 'full'
  },
  {
    path: 'location-materiel',
    loadComponent: () => import('./material-inventory/material-inventory.component').then(m => m.MaterialInventoryComponent),
    data: { breadcrumb: { label: 'Inventaire Matériel' } }
  },
  {
    path: ':category',
    loadComponent: () => import('./services-config/services-config.component').then(m => m.ServicesConfigComponent),
    data: { breadcrumb: { label: 'Configuration Services' } }
  }
];

import { Component, OnInit } from '@angular/core';
import {RouterLink, RouterLinkActive, RouterOutlet, Router, NavigationEnd, ActivatedRoute} from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { filter } from 'rxjs/operators';

interface BreadcrumbItem {
  label: string;
  url?: string;
  icon?: string;
}

@Component({
  selector: 'app-root',
  imports: [RouterLink, RouterOutlet, NzIconModule, NzLayoutModule, NzMenuModule, RouterLinkActive, NzBreadCrumbModule, NzAvatarModule, NzDropDownModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  isCollapsed = false;
  breadcrumbs: BreadcrumbItem[] = [];
  currentUser = {
    name: 'Ahmed Benali',
    email: '<EMAIL>',
    tenant: 'Traiteria Premium',
    avatar: 'AB'
  };

  // Gestion des sous-menus ouverts
  openSubmenus: Set<string> = new Set();
  activeSubmenu: string | null = null;

  constructor(private router: Router, private activatedRoute: ActivatedRoute) {}

  ngOnInit() {
    // Écouter les changements de route pour mettre à jour le breadcrumb
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.updateBreadcrumbs();
        this.updateActiveSubmenu();
      });

    // Initialiser le breadcrumb et le sous-menu actif
    this.updateBreadcrumbs();
    this.updateActiveSubmenu();
  }

  isEventsListActive(): boolean {
    const url = this.router.url;
    return url === '/events' || url.startsWith('/events/edit/');
  }

  isClientsListActive(): boolean {
    const url = this.router.url;
    return url === '/clients' || url.startsWith('/clients/edit/');
  }

  isMenusListActive(): boolean {
    const url = this.router.url;
    return url === '/menus' || url.startsWith('/menus/edit/');
  }

  private updateBreadcrumbs() {
    this.breadcrumbs = [];

    // Construire le breadcrumb à partir des données des routes
    let route = this.activatedRoute;
    let url = '';

    // Remonter jusqu'à la route racine
    while (route.parent) {
      route = route.parent;
    }

    // Parcourir les routes enfants
    const breadcrumbData = this.getBreadcrumbData(route);

    // Toujours commencer par Accueil si on n'est pas déjà dessus
    if (!this.router.url.startsWith('/dashboard')) {
      this.breadcrumbs.push({ label: 'Accueil', url: '/dashboard', icon: 'home' });
    }

    // Ajouter les breadcrumbs des routes
    breadcrumbData.forEach((item, index) => {
      if (index === breadcrumbData.length - 1) {
        // Dernier élément - pas de lien
        this.breadcrumbs.push({ label: item.label, icon: item.icon });
      } else {
        // Éléments intermédiaires - avec lien
        this.breadcrumbs.push({ label: item.label, url: item.url, icon: item.icon });
      }
    });
  }

  private getBreadcrumbData(route: ActivatedRoute): any[] {
    const breadcrumbs: any[] = [];
    const url = this.router.url;

    if (url === '/welcome') {
      breadcrumbs.push({ label: 'Dashboard', icon: 'home' });
    } else if (url.startsWith('/dashboard')) {
      breadcrumbs.push({ label: 'Dashboard', url: '/dashboard', icon: 'dashboard' });

      if (url === '/dashboard/statistics') {
        breadcrumbs.push({ label: 'Statistiques', icon: 'bar-chart' });
      } else if (url === '/dashboard/reports') {
        breadcrumbs.push({ label: 'Rapports', icon: 'file-text' });
      } else if (url === '/dashboard' || url === '/dashboard/overview') {
        breadcrumbs[breadcrumbs.length - 1] = { label: 'Vue d\'ensemble', icon: 'dashboard' };
      }
    } else if (url.startsWith('/events')) {
      breadcrumbs.push({ label: 'Événements', url: '/events', icon: 'calendar' });

      if (url === '/events/new') {
        breadcrumbs.push({ label: 'Nouvel événement', icon: 'plus' });
      } else if (url.startsWith('/events/edit/')) {
        breadcrumbs.push({ label: 'Modifier événement', icon: 'edit' });
      } else if (url === '/events/calendar') {
        breadcrumbs.push({ label: 'Calendrier des événements', icon: 'calendar' });
      } else if (url === '/events') {
        breadcrumbs[breadcrumbs.length - 1] = { label: 'Liste des événements', icon: 'calendar' };
      }
    } else if (url.startsWith('/clients')) {
      breadcrumbs.push({ label: 'Clients', url: '/clients', icon: 'user' });

      if (url === '/clients/new') {
        breadcrumbs.push({ label: 'Nouveau client', icon: 'plus' });
      } else if (url.startsWith('/clients/edit/')) {
        breadcrumbs.push({ label: 'Modifier client', icon: 'edit' });
      } else if (url === '/clients') {
        breadcrumbs[breadcrumbs.length - 1] = { label: 'Liste des clients', icon: 'user' };
      }
    } else if (url.startsWith('/menus')) {
      breadcrumbs.push({ label: 'Menus', url: '/menus', icon: 'menu' });

      if (url === '/menus/new') {
        breadcrumbs.push({ label: 'Nouveau menu', icon: 'plus' });
      } else if (url.startsWith('/menus/edit/')) {
        breadcrumbs.push({ label: 'Modifier menu', icon: 'edit' });
      } else if (url === '/menus') {
        breadcrumbs[breadcrumbs.length - 1] = { label: 'Visualiser les menus', icon: 'menu' };
      }
    } else if (url.startsWith('/services')) {
      breadcrumbs.push({ label: 'Services', url: '/services', icon: 'setting' });

      if (url.includes('/particulier')) {
        breadcrumbs.push({ label: 'Configuration Particulier', icon: 'user' });
      } else if (url.includes('/entreprise')) {
        breadcrumbs.push({ label: 'Configuration Entreprise', icon: 'bank' });
      } else if (url.includes('/evenements-speciaux')) {
        breadcrumbs.push({ label: 'Configuration Événements Spéciaux', icon: 'star' });
      } else if (url.includes('/location-materiel')) {
        breadcrumbs.push({ label: 'Inventaire Matériel', icon: 'tool' });
      }
    }

    return breadcrumbs;
  }

  // Méthodes de gestion des sous-menus
  updateActiveSubmenu(): void {
    const url = this.router.url;

    if (url.startsWith('/dashboard') || url === '/welcome' || url === '/') {
      this.activeSubmenu = 'dashboard';
    } else if (url.startsWith('/events')) {
      this.activeSubmenu = 'events';
    } else if (url.startsWith('/clients')) {
      this.activeSubmenu = 'clients';
    } else if (url.startsWith('/menus')) {
      this.activeSubmenu = 'menus';
    } else if (url.startsWith('/services')) {
      this.activeSubmenu = 'services';
    } else {
      this.activeSubmenu = null;
    }

    // S'assurer que le sous-menu actif est toujours ouvert
    if (this.activeSubmenu) {
      this.openSubmenus.add(this.activeSubmenu);
    }
  }

  isSubmenuOpen(submenuKey: string): boolean {
    return this.openSubmenus.has(submenuKey);
  }

  onSubmenuOpenChange(submenuKey: string, isOpen: boolean): void {
    if (isOpen) {
      // Si on ouvre un nouveau sous-menu
      this.openSubmenus.add(submenuKey);

      // Vérifier si on a plus de 2 sous-menus ouverts
      if (this.openSubmenus.size > 2) {
        // Fermer le plus ancien qui n'est pas le sous-menu actif
        for (const openSubmenu of this.openSubmenus) {
          if (openSubmenu !== this.activeSubmenu && openSubmenu !== submenuKey) {
            this.openSubmenus.delete(openSubmenu);
            break;
          }
        }
      }
    } else {
      // Si on ferme un sous-menu, s'assurer que ce n'est pas le sous-menu actif
      if (submenuKey !== this.activeSubmenu) {
        this.openSubmenus.delete(submenuKey);
      }
    }
  }
}

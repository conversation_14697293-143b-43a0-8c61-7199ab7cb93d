import { Component, Input, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormGroup, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';

@Component({
  selector: 'app-client-form-inline',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzFormModule,
    NzInputModule,
    NzAutocompleteModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ClientFormInlineComponent),
      multi: true
    }
  ],
  template: `
    <div [formGroup]="clientForm">
      <!-- Nom et Prénom sur la même ligne -->
      <div class="form-row">
        <div class="form-col">
          <nz-form-item>
            <nz-form-label nzRequired>Nom</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('nom')">
              <input nz-input formControlName="nom" placeholder="Nom du client"/>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div class="form-col">
          <nz-form-item>
            <nz-form-label nzRequired>Prénom</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('prenom')">
              <input nz-input formControlName="prenom" placeholder="Prénom du client"/>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <div class="form-row">
        <div class="form-col">
          <nz-form-item>
            <nz-form-label nzRequired>CIN</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('cin')">
              <input nz-input formControlName="cin" placeholder="CIN"/>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div class="form-col">
          <nz-form-item>
            <nz-form-label nzRequired>Ville</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('ville')">
              <input
                nz-input
                [(ngModel)]="citySearchValue"
                [ngModelOptions]="{standalone: true}"
                (ngModelChange)="onCitySearch($event)"
                placeholder="Ville"
                [nzAutocomplete]="cityAutocomplete">
              <nz-autocomplete #cityAutocomplete>
                @for (city of filteredCities; track city) {
                  <nz-auto-option [nzValue]="city" (click)="onCitySelect(city)">
                    {{ city }}
                  </nz-auto-option>
                }
              </nz-autocomplete>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <nz-form-item>
        <nz-form-label>Adresse</nz-form-label>
        <nz-form-control [nzErrorTip]="getFieldError('adresse')">
          <textarea nz-input formControlName="adresse" placeholder="Adresse complète (optionnel)" rows="2"></textarea>
        </nz-form-control>
      </nz-form-item>

      <div class="form-row">
        <div class="form-col">
          <nz-form-item>
            <nz-form-label nzRequired>Téléphone 1</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('telephone1')">
              <input nz-input formControlName="telephone1" placeholder="Téléphone principal"/>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div class="form-col">
          <nz-form-item>
            <nz-form-label>Téléphone 2</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('telephone2')">
              <input nz-input formControlName="telephone2" placeholder="Téléphone secondaire"/>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <nz-form-item>
        <nz-form-label>Email</nz-form-label>
        <nz-form-control [nzErrorTip]="getFieldError('email')">
          <input nz-input formControlName="email" placeholder="Adresse email (optionnel)" type="email"/>
        </nz-form-control>
      </nz-form-item>
    </div>
  `,
  styleUrls: ['./client-form-inline.component.css']
})
export class ClientFormInlineComponent implements ControlValueAccessor {
  @Input() clientForm!: FormGroup;
  @Input() availableCities: string[] = [];
  @Input() getFieldError!: (fieldName: string) => string;

  filteredCities: string[] = [];
  citySearchValue = '';

  private onChange = (value: any) => {};
  private onTouched = () => {};

  ngOnInit(): void {
    this.filteredCities = [...this.availableCities];
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    if (value) {
      this.clientForm.patchValue(value);
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  onCitySearch(value: string): void {
    if (!value) {
      this.filteredCities = [...this.availableCities];
      return;
    }
    
    this.filteredCities = this.availableCities.filter(city =>
      city.toLowerCase().includes(value.toLowerCase())
    );
  }

  onCitySelect(city: string): void {
    this.clientForm.patchValue({ ville: city });
    this.citySearchValue = city;
    this.clientForm.get('ville')?.markAsTouched();
    this.clientForm.get('ville')?.updateValueAndValidity();
  }
}

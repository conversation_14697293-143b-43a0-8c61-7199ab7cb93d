/* Form layout */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-col {
  flex: 1;
}

.form-row .form-col:first-child {
  margin-right: 8px;
}

.form-row .form-col:last-child {
  margin-left: 8px;
}

/* Form items */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item-label {
  padding-bottom: 4px;
}

/* Input styling */
.ant-input,
.ant-input:focus,
.ant-input-focused {
  border-radius: var(--radius-md);
  border-color: var(--apple-gray-300);
  box-shadow: none;
}

.ant-input:focus,
.ant-input-focused {
  border-color: var(--apple-grenat);
  box-shadow: 0 0 0 2px var(--apple-grenat-pale);
}

/* Error states */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input:focus,
.ant-form-item-has-error .ant-input-focused {
  border-color: #ff4d4f;
}

.ant-form-item-has-error .ant-input:focus,
.ant-form-item-has-error .ant-input-focused {
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* Responsive */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-col {
    width: 100%;
    margin: 0 !important;
  }
}

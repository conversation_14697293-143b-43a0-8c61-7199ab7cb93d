import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';

export interface PaymentData {
  eventId: number;
  montant: number;
  date: Date;
  mode: 'especes' | 'cheque' | 'virement' | 'carte';
  notes?: string;
}

export interface PaymentModalData {
  eventId: number;
  eventType: string;
  clientName: string;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
}

@Component({
  selector: 'app-quick-payment-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzInputNumberModule,
    NzSelectModule,
    NzDatePickerModule,
    NzButtonModule
  ],
  template: `
    <div class="quick-payment-modal">
      <!-- Header info -->
      <div class="payment-header">
        <h3>{{ modalData.eventType }} - {{ modalData.clientName }}</h3>
        <div class="payment-summary">
          <div class="summary-item">
            <span class="label">Total:</span>
            <span class="amount total">{{ formatCurrency(modalData.totalAmount) }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Payé:</span>
            <span class="amount paid">{{ formatCurrency(modalData.paidAmount) }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Restant:</span>
            <span class="amount remaining">{{ formatCurrency(modalData.remainingAmount) }}</span>
          </div>
        </div>
      </div>

      <!-- Form -->
      <form [formGroup]="paymentForm" class="payment-form">
        <!-- Montant -->
        <nz-form-item>
          <nz-form-label nzRequired>Montant</nz-form-label>
          <nz-form-control [nzErrorTip]="getFieldError('montant')">
            <nz-input-number
              formControlName="montant"
              [nzMin]="1"
              [nzMax]="modalData.remainingAmount"
              nzPlaceHolder="Montant du paiement"
              [nzFormatter]="currencyFormatter"
              [nzParser]="+currencyParser"
              style="width: 100%">
            </nz-input-number>
          </nz-form-control>
        </nz-form-item>

        <!-- Boutons montants rapides -->
        <div class="quick-amounts">
          <span class="quick-label">Montants rapides:</span>
          <div class="quick-buttons">
            <button
              nz-button
              nzSize="small"
              type="button"
              (click)="setAmount(advanceAmount)"
              [disabled]="advanceAmount > modalData.remainingAmount">
              Avance ({{ formatCurrency(advanceAmount) }})
            </button>
            <button
              nz-button
              nzSize="small"
              type="button"
              (click)="setAmount(halfAmount)"
              [disabled]="halfAmount > modalData.remainingAmount">
              50% ({{ formatCurrency(halfAmount) }})
            </button>
            <button
              nz-button
              nzSize="small"
              type="button"
              (click)="setAmount(modalData.remainingAmount)">
              Solde ({{ formatCurrency(modalData.remainingAmount) }})
            </button>
          </div>
        </div>

        <!-- Date -->
        <nz-form-item>
          <nz-form-label nzRequired>Date</nz-form-label>
          <nz-form-control [nzErrorTip]="getFieldError('date')">
            <nz-date-picker
              formControlName="date"
              style="width: 100%"
              nzPlaceHolder="Date du paiement">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>

        <!-- Mode de paiement -->
        <nz-form-item>
          <nz-form-label nzRequired>Mode de paiement</nz-form-label>
          <nz-form-control [nzErrorTip]="getFieldError('mode')">
            <nz-select formControlName="mode" nzPlaceHolder="Sélectionner le mode">
              <nz-option nzValue="especes" nzLabel="💵 Espèces"></nz-option>
              <nz-option nzValue="cheque" nzLabel="📄 Chèque"></nz-option>
              <nz-option nzValue="virement" nzLabel="🏦 Virement"></nz-option>
              <nz-option nzValue="carte" nzLabel="💳 Carte bancaire"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>

        <!-- Notes -->
        <nz-form-item>
          <nz-form-label>Notes</nz-form-label>
          <nz-form-control>
            <textarea
              nz-input
              formControlName="notes"
              placeholder="Notes sur le paiement (optionnel)"
              rows="2">
            </textarea>
          </nz-form-control>
        </nz-form-item>
      </form>

      <!-- Actions -->
      <div class="modal-actions">
        <button nz-button nzType="default" (click)="cancel()">
          Annuler
        </button>
        <button
          nz-button
          nzType="primary"
          (click)="save()"
          [nzLoading]="saving"
          [disabled]="!paymentForm.valid">
          Enregistrer le paiement
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./quick-payment-modal.component.css']
})
export class QuickPaymentModalComponent implements OnInit {
  @Input() modalData!: PaymentModalData;

  paymentForm!: FormGroup;
  saving = false;

  // Montants prédéfinis
  get advanceAmount(): number {
    return Math.round(this.modalData.totalAmount * 0.3); // 30% d'avance
  }

  get halfAmount(): number {
    return Math.round(this.modalData.totalAmount * 0.5); // 50%
  }

  constructor(
    private fb: FormBuilder,
    private modal: NzModalRef,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.paymentForm = this.fb.group({
      montant: [null, [Validators.required, Validators.min(1), Validators.max(this.modalData.remainingAmount)]],
      date: [new Date(), Validators.required],
      mode: [null, Validators.required],
      notes: ['']
    });
  }

  setAmount(amount: number): void {
    if (amount <= this.modalData.remainingAmount) {
      this.paymentForm.patchValue({ montant: amount });
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.paymentForm.get(fieldName);
    if (field?.touched && field?.errors) {
      if (field.errors['required']) return `${this.getFieldLabel(fieldName)} est requis`;
      if (field.errors['min']) return 'Le montant doit être supérieur à 0';
      if (field.errors['max']) return 'Le montant ne peut pas dépasser le montant restant';
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      'montant': 'Le montant',
      'date': 'La date',
      'mode': 'Le mode de paiement'
    };
    return labels[fieldName] || fieldName;
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount);
  }

  currencyFormatter = (value: number): string => {
    return `${value} MAD`;
  };

  currencyParser = (value: string): string => {
    return value.replace(' MAD', '');
  };

  save(): void {
    if (this.paymentForm.valid) {
      this.saving = true;

      const paymentData: PaymentData = {
        eventId: this.modalData.eventId,
        montant: this.paymentForm.value.montant,
        date: this.paymentForm.value.date,
        mode: this.paymentForm.value.mode,
        notes: this.paymentForm.value.notes
      };

      // Simulation d'un appel API
      setTimeout(() => {
        this.saving = false;
        this.message.success(`Paiement de ${this.formatCurrency(paymentData.montant)} enregistré avec succès !`);
        this.modal.close(paymentData);
      }, 1000);
    } else {
      this.markFormGroupTouched();
    }
  }

  cancel(): void {
    this.modal.close();
  }

  private markFormGroupTouched(): void {
    Object.keys(this.paymentForm.controls).forEach(key => {
      const control = this.paymentForm.get(key);
      control?.markAsTouched();
      control?.updateValueAndValidity();
    });
  }
}

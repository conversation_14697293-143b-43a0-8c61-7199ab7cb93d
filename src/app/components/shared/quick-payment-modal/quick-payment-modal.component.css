/* Container principal */
.quick-payment-modal {
  padding: 0;
}

/* Header avec informations de l'événement */
.payment-header {
  background: linear-gradient(135deg, var(--apple-grenat-pale) 0%, #f8f9fa 100%);
  padding: 20px;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  margin: -24px -24px 24px -24px;
  border-bottom: 1px solid var(--apple-gray-200);
}

.payment-header h3 {
  margin: 0 0 16px 0;
  color: var(--apple-black);
  font-size: 18px;
  font-weight: 600;
}

/* Résumé des montants */
.payment-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.summary-item .label {
  font-size: 12px;
  color: var(--apple-gray-600);
  margin-bottom: 4px;
  text-transform: uppercase;
  font-weight: 500;
}

.summary-item .amount {
  font-size: 16px;
  font-weight: 700;
}

.amount.total {
  color: var(--apple-black);
}

.amount.paid {
  color: #52c41a;
}

.amount.remaining {
  color: var(--apple-grenat);
}

/* Formulaire */
.payment-form {
  margin-bottom: 24px;
}

.payment-form .ant-form-item {
  margin-bottom: 20px;
}

.payment-form .ant-form-item-label {
  padding-bottom: 4px;
}

.payment-form .ant-form-item-label > label {
  font-weight: 600;
  color: var(--apple-gray-700);
}

/* Boutons montants rapides */
.quick-amounts {
  margin: 16px 0 24px 0;
  padding: 16px;
  background: var(--apple-gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--apple-gray-200);
}

.quick-label {
  display: block;
  font-size: 12px;
  color: var(--apple-gray-600);
  margin-bottom: 8px;
  font-weight: 500;
  text-transform: uppercase;
}

.quick-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-buttons button {
  border-color: var(--apple-grenat);
  color: var(--apple-grenat);
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
}

.quick-buttons button:hover:not(:disabled) {
  background: var(--apple-grenat);
  color: white;
  border-color: var(--apple-grenat);
}

.quick-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Inputs */
.ant-input,
.ant-input-number,
.ant-select-selector,
.ant-picker {
  border-radius: var(--radius-md);
  border-color: var(--apple-gray-300);
}

.ant-input:focus,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector,
.ant-picker-focused {
  border-color: var(--apple-grenat);
  box-shadow: 0 0 0 2px var(--apple-grenat-pale);
}

/* Input number personnalisé */
.ant-input-number {
  width: 100%;
}

.ant-input-number-handler-wrap {
  border-left-color: var(--apple-gray-300);
}

.ant-input-number-handler {
  border-color: var(--apple-gray-300);
}

.ant-input-number-handler:hover {
  color: var(--apple-grenat);
}

/* Select avec emojis */
.ant-select-item-option-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Actions du modal */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid var(--apple-gray-200);
  margin-top: 24px;
}

.modal-actions button {
  min-width: 100px;
}

/* Messages d'erreur */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input-number,
.ant-form-item-has-error .ant-select-selector,
.ant-form-item-has-error .ant-picker {
  border-color: #ff4d4f;
}

.ant-form-item-has-error .ant-input:focus,
.ant-form-item-has-error .ant-input-number-focused,
.ant-form-item-has-error .ant-select-focused .ant-select-selector,
.ant-form-item-has-error .ant-picker-focused {
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* Responsive */
@media (max-width: 480px) {
  .payment-summary {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .quick-buttons {
    flex-direction: column;
  }
  
  .quick-buttons button {
    width: 100%;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-actions button {
    width: 100%;
  }
}

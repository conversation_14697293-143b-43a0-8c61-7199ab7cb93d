import { Component, signal, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { NzStepsModule } from 'ng-zorro-antd/steps';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';

export interface EventType {
  value: string;
  label: string;
  category: 'particulier' | 'entreprise' | 'evenements-speciaux' | 'location-materiel';
}

export interface Client {
  id: string;
  nom: string;
  prenom: string;
  cin: string;
  ville: string;
  adresse: string;
  telephone1: string;
  telephone2?: string;
  email: string;
}

export interface EventMaterial {
  materialId: string;
  materialName: string;
  quantity: number;
  unit: 'table' | 'evenement' | 'personne';
  availableQuantity: number;
  isOverLimit: boolean;
}

export interface MaterialItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  unit: string;
  category: string;
}

@Component({
  selector: 'app-event-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    DragDropModule,
    NzStepsModule,
    NzButtonModule,
    NzCardModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
    NzRadioModule,
    NzDatePickerModule,
    NzInputNumberModule,
    NzSwitchModule,
    NzDrawerModule,
    NzIconModule,
    NzDividerModule,
    NzTagModule,
    NzToolTipModule,
    NzAutocompleteModule
  ],
  templateUrl: './event-form.component.html',
  styleUrl: './event-form.component.css'
})
export class EventFormComponent implements OnInit {
  currentStep = signal(0);
  eventId: string | null = null;
  isEditMode = false;
  preselectedDate: Date | null = null;
  isFromCalendar = false;

  // Formulaires pour chaque étape
  step1Form!: FormGroup;
  step2Form!: FormGroup;
  step3Form!: FormGroup;
  step3LocationForm!: FormGroup; // Formulaire spécial pour location de matériel
  step4Form!: FormGroup;
  step5Form!: FormGroup;

  // Gestion des matériels
  availableMaterials: MaterialItem[] = [];
  eventMaterials: EventMaterial[] = [];

  // Autocomplete pour clients
  filteredClients: Client[] = [];
  clientSearchValue = '';

  // Types d'événements
  eventTypes: EventType[] = [
    // Particulier
    { value: 'mariage', label: 'Mariage', category: 'particulier' },
    { value: 'fiancailles', label: 'Fiançailles', category: 'particulier' },
    { value: 'aqiqah', label: 'Aqiqah', category: 'particulier' },
    { value: 'repas-livre-part', label: 'Repas livré', category: 'particulier' },
    { value: 'soutenance', label: 'Soutenance', category: 'particulier' },
    { value: 'funerailles', label: 'Funérailles', category: 'particulier' },
    { value: 'anniversaire', label: 'Anniversaire', category: 'particulier' },
    { value: 'buffet-part', label: 'Buffet', category: 'particulier' },
    { value: 'brunch-part', label: 'Brunch', category: 'particulier' },
    { value: 'ftour-part', label: 'Ftour', category: 'particulier' },
    { value: 'cocktails-dinatoire-part', label: 'Cocktails dînatoire', category: 'particulier' },
    { value: 'cocktails-part', label: 'Cocktails', category: 'particulier' },

    // Entreprise
    { value: 'manifestation', label: 'Manifestation ou fête de travail', category: 'entreprise' },
    { value: 'buffet-ent', label: 'Buffet', category: 'entreprise' },
    { value: 'repas-seminaire', label: 'Repas séminaire', category: 'entreprise' },
    { value: 'cocktails-ent', label: 'Cocktails', category: 'entreprise' },
    { value: 'cocktails-dinatoire-ent', label: 'Cocktails dînatoire', category: 'entreprise' },
    { value: 'pause-cafe', label: 'Pause café', category: 'entreprise' },
    { value: 'lunch-box', label: 'Lunch box', category: 'entreprise' },
    { value: 'brunch-ent', label: 'Brunch', category: 'entreprise' },

    // Événements spéciaux
    { value: 'cinema', label: 'Cinéma', category: 'evenements-speciaux' },
    { value: 'ceremonie', label: 'Cérémonie', category: 'evenements-speciaux' },

    // Location de matériel
    { value: 'chapiteaux', label: 'Chapiteaux', category: 'location-materiel' },
    { value: 'estrade', label: 'Estrade', category: 'location-materiel' },
    { value: 'dalo', label: 'Dalo', category: 'location-materiel' },
    { value: 'scene', label: 'Scène', category: 'location-materiel' },
    { value: 'salle-fete', label: 'Salle de fête', category: 'location-materiel' }
  ];

  // Menus disponibles
  availableMenus = [
    { id: '1', title: 'Menu Sultana' },
    { id: '2', title: 'Menu Royal' },
    { id: '3', title: 'Menu Business' },
    { id: '4', title: 'Menu Traditionnel' },
    { id: '5', title: 'Menu Végétarien' }
  ];

  // Clients existants (à remplacer par un service)
  existingClients: Client[] = [
    {
      id: '1',
      nom: 'Alami',
      prenom: 'Ahmed',
      cin: 'AB123456',
      ville: 'Casablanca',
      adresse: '123 Rue Mohammed V',
      telephone1: '0612345678',
      telephone2: '0522123456',
      email: '<EMAIL>'
    }
  ];

  // Options pour les packs
  packOptions = {
    mariage: ['bouffe', 'salle', 'negaffa', 'amaria', 'maida'],
    feteTravail: ['bouffe', 'salle']
  };

  // Services disponibles pour drag and drop
  availableServices = [
    { id: 'photographe', label: 'Photographe', category: 'Services' },
    { id: 'dj', label: 'DJ', category: 'Services' },
    { id: 'issawa', label: 'Issawa', category: 'Services' },
    { id: 'deqa-marrakchia', label: 'Deqa Marrakchia', category: 'Services' },
    { id: 'ftour', label: 'Ftour', category: 'Services' },
    { id: 'piece-montee', label: 'Pièce montée', category: 'Services' },
    { id: 'orchestre-celebre', label: 'Orchestre célèbre', category: 'Orchestre' },
    { id: 'orchestre-standard', label: 'Orchestre standard', category: 'Orchestre' },
    { id: 'decoration-florale', label: 'Décoration florale', category: 'Décoration' },
    { id: 'decoration-lumiere', label: 'Décoration lumière', category: 'Décoration' },
    { id: 'materiel-standard', label: 'Matériel standard', category: 'Matériel' },
    { id: 'materiel-luxe', label: 'Matériel luxe', category: 'Matériel' }
  ];

  selectedServices: any[] = [];
  filteredServices: any[] = [];
  searchTerm = '';
  newServiceInput = '';

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private message: NzMessageService
  ) {
    this.initializeForms();
  }

  ngOnInit(): void {
    this.eventId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.eventId;
    this.updateFilteredServices();
    this.setupTableCalculation();
    this.loadAvailableMaterials();
    this.loadExistingClients();

    // Initialiser la liste filtrée des clients
    this.filteredClients = [...this.existingClients];

    if (this.isEditMode && this.eventId) {
      this.loadEventData(this.eventId);
    } else {
      // Vérifier s'il y a une date pré-sélectionnée depuis le calendrier
      this.checkPreselectedDate();
    }
  }

  private initializeForms(): void {
    // Étape 1: Type d'événement
    this.step1Form = this.fb.group({
      category: ['', Validators.required],
      eventType: ['', Validators.required]
    });

    // Étape 2: Client
    this.step2Form = this.fb.group({
      clientType: ['existing', Validators.required],
      selectedClient: ['', Validators.required],
      newClient: this.fb.group({
        nom: [''],
        prenom: [''],
        cin: [''],
        ville: [''],
        adresse: [''], // Pas obligatoire pour le client
        telephone1: [''],
        telephone2: [''],
        email: [''] // Pas obligatoire pour le client
      })
    });

    // Étape 3: Détails événement
    this.step3Form = this.fb.group({
      nbPersonnes: [null, [Validators.required, Validators.min(1)]],
      personnesParTable: [8, [Validators.required, Validators.min(1)]],
      dateDebut: [null, Validators.required],
      dateFin: [null], // Optionnel
      ville: ['', Validators.required],
      menus: [[]],
      menuNotes: [''], // Notes sur le menu
      adresse: ['', Validators.required],
      theme: [''],
      specialOptions: [[]],
      serviceNotes: [''], // Notes sur les services
      // Champs pour les matériels
      selectedMaterialId: [''],
      materialQuantity: [1, [Validators.min(1)]],
      materialUnit: ['table'] // Pour événements normaux, 'unite' pour location
    });

    // Étape 4: Paiement
    this.step4Form = this.fb.group({
      statut: ['non-paye', Validators.required],
      montantTotal: [null, [Validators.required, Validators.min(0)]],
      montantPaye: [0, [Validators.min(0)]],
      methodePaiement: [''],
      dateEcheance: [null],
      commentairePaiement: ['']
    });

    // Étape 5: Source et commentaires
    this.step5Form = this.fb.group({
      sourceTraffic: [[]], // Multi-select array
      commentaires: ['']
    });

    // Formulaire spécial pour location de matériel (étape 3)
    this.step3LocationForm = this.fb.group({
      dateDebut: [null, Validators.required],
      dateFin: [null], // Optionnel
      ville: ['', Validators.required],
      adresse: ['', Validators.required],
      // Champs pour les matériels
      selectedMaterialId: [''],
      materialQuantity: [1, [Validators.min(1)]],
      materialUnit: ['unite'] // Par défaut "unite" pour location
    });
  }

  get filteredEventTypes(): EventType[] {
    const category = this.step1Form.get('category')?.value;
    return category ? this.eventTypes.filter(type => type.category === category) : [];
  }

  get selectedEventType(): string {
    return this.step1Form.get('eventType')?.value;
  }

  // Méthodes pour gérer le drag and drop
  drop(event: CdkDragDrop<any[]>): void {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
      this.updateFilteredServices();
      this.updateFormData();
    }
  }

  // Recherche dans les services disponibles
  onSearchChange(): void {
    console.log('Search term:', this.searchTerm);
    this.updateFilteredServices();
  }

  private updateFilteredServices(): void {
    // Commencer avec tous les services disponibles
    let allAvailable = this.availableServices.filter(
      service => !this.selectedServices.find(selected => selected.id === service.id)
    );

    // Appliquer le filtre de recherche si il y a un terme
    if (this.searchTerm && this.searchTerm.trim().length > 0) {
      const searchLower = this.searchTerm.toLowerCase().trim();
      console.log('Filtering with:', searchLower);
      allAvailable = allAvailable.filter(service => {
        const matchLabel = service.label.toLowerCase().includes(searchLower);
        const matchCategory = service.category.toLowerCase().includes(searchLower);
        return matchLabel || matchCategory;
      });
    }

    this.filteredServices = allAvailable;
    console.log('Filtered services:', this.filteredServices.length);
  }

  // Ajouter un nouveau service personnalisé
  addNewService(): void {
    const trimmedInput = this.newServiceInput?.trim();
    console.log('Adding service:', trimmedInput);

    if (trimmedInput && trimmedInput.length > 0) {
      // Vérifier si le service n'existe pas déjà
      const exists = this.availableServices.find(s =>
        s.label.toLowerCase() === trimmedInput.toLowerCase()
      );

      if (!exists) {
        const newService = {
          id: `custom-${Date.now()}`,
          label: trimmedInput,
          category: 'Personnalisé'
        };

        console.log('New service created:', newService);
        this.availableServices.push(newService);
        this.updateFilteredServices();
        console.log('Available services count:', this.availableServices.length);
      } else {
        console.log('Service already exists');
      }

      this.newServiceInput = '';
    } else {
      console.log('No input to add');
    }
  }

  // Mettre à jour les données du formulaire
  private updateFormData(): void {
    const selectedIds = this.selectedServices.map(service => service.id);
    this.step3Form.patchValue({ specialOptions: selectedIds });
  }

  // Supprimer un service sélectionné (retour vers disponibles)
  removeSelectedService(service: any): void {
    const index = this.selectedServices.findIndex(s => s.id === service.id);
    if (index > -1) {
      this.selectedServices.splice(index, 1);
      this.updateFilteredServices();
      this.updateFormData();
    }
  }

  // Supprimer un service disponible (seulement les services personnalisés)
  removeAvailableService(service: any, event: Event): void {
    event.stopPropagation(); // Empêcher le drag

    if (service.id.startsWith('custom-')) {
      // Supprimer des services disponibles
      const availableIndex = this.availableServices.findIndex(s => s.id === service.id);
      if (availableIndex > -1) {
        this.availableServices.splice(availableIndex, 1);
      }

      // Supprimer des services sélectionnés si présent
      const selectedIndex = this.selectedServices.findIndex(s => s.id === service.id);
      if (selectedIndex > -1) {
        this.selectedServices.splice(selectedIndex, 1);
        this.updateFormData();
      }

      this.updateFilteredServices();
    }
  }

  get isClientTypeNew(): boolean {
    return this.step2Form.get('clientType')?.value === 'new';
  }

  nextStep(): void {
    // Toujours marquer les champs comme touchés pour afficher les erreurs
    this.markCurrentStepAsTouched();

    // Forcer la mise à jour pour que les erreurs s'affichent
    this.forceErrorUpdate();

    if (this.validateCurrentStep()) {
      this.currentStep.update(step => Math.min(step + 1, 4));
    }
  }

  prevStep(): void {
    this.currentStep.update(step => Math.max(step - 1, 0));
  }

  private validateCurrentStep(): boolean {
    const currentStepIndex = this.currentStep();

    // Pour l'étape 3, utiliser le bon FormGroup selon le type
    let step3FormToUse = this.step3Form;
    if (currentStepIndex === 2 && this.isLocationMateriel()) {
      step3FormToUse = this.step3LocationForm;
    }

    const forms = [this.step1Form, this.step2Form, step3FormToUse, this.step4Form, this.step5Form];

    if (currentStepIndex < forms.length) {
      const currentForm = forms[currentStepIndex];

      if (currentStepIndex === 1 && this.isClientTypeNew) {
        // Valider le formulaire client si nouveau client
        const newClientForm = this.step2Form.get('newClient') as FormGroup;
        this.markFormGroupTouched(newClientForm);
        return newClientForm.valid;
      }

      this.markFormGroupTouched(currentForm);
      return currentForm.valid;
    }

    return true;
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
      control?.markAsDirty();
      control?.updateValueAndValidity();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
    formGroup.markAsTouched();
    formGroup.markAsDirty();
    formGroup.updateValueAndValidity();
  }



  onCategoryChange(): void {
    const category = this.step1Form.get('category')?.value;

    // Pour location de matériel, on met un type par défaut
    if (category === 'location-materiel') {
      this.step1Form.patchValue({ eventType: 'Location de matériel' });
      // Forcer la validation
      this.step1Form.get('eventType')?.updateValueAndValidity();

      // Réinitialiser le formulaire de location
      this.step3LocationForm.reset({
        dateDebut: null,
        dateFin: null,
        ville: '',
        adresse: '',
        selectedMaterialId: '',
        materialQuantity: 1,
        materialUnit: 'unite'
      });
    } else {
      this.step1Form.patchValue({ eventType: '' });
    }
  }

  selectCategory(category: 'particulier' | 'entreprise' | 'evenements-speciaux' | 'location-materiel'): void {
    if (category === 'location-materiel') {
      this.step1Form.patchValue({
        category: category,
        eventType: 'Location de matériel'
      });
      // Forcer la validation
      this.step1Form.get('eventType')?.updateValueAndValidity();

      // Réinitialiser le formulaire de location
      this.step3LocationForm.reset({
        dateDebut: null,
        dateFin: null,
        ville: '',
        adresse: '',
        selectedMaterialId: '',
        materialQuantity: 1,
        materialUnit: 'unite'
      });
    } else {
      this.step1Form.patchValue({
        category: category,
        eventType: ''
      });
    }
  }

  onClientTypeChange(): void {
    const clientType = this.step2Form.get('clientType')?.value;
    const selectedClientControl = this.step2Form.get('selectedClient');
    const newClientGroup = this.step2Form.get('newClient') as FormGroup;

    if (clientType === 'existing') {
      // Activer la validation pour client existant
      selectedClientControl?.setValidators([Validators.required]);
      // Désactiver la validation pour nouveau client
      this.clearNewClientValidators(newClientGroup);
    } else {
      // Désactiver la validation pour client existant
      selectedClientControl?.clearValidators();
      selectedClientControl?.setValue('');
      // Activer la validation pour nouveau client
      this.setNewClientValidators(newClientGroup);
    }

    selectedClientControl?.updateValueAndValidity();
    newClientGroup?.updateValueAndValidity();
  }

  private setNewClientValidators(group: FormGroup): void {
    group.get('nom')?.setValidators([Validators.required, Validators.minLength(2)]);
    group.get('prenom')?.setValidators([Validators.required, Validators.minLength(2)]);
    // CIN: 1-3 lettres + 4-6 chiffres
    group.get('cin')?.setValidators([Validators.required, Validators.pattern(/^[A-Z]{1,3}[0-9]{4,6}$/), Validators.minLength(6)]);
    group.get('ville')?.setValidators([Validators.required, Validators.minLength(2)]);
    // Adresse client pas obligatoire
    group.get('adresse')?.setValidators([Validators.minLength(5)]);
    // Téléphone avec support international (+...)
    group.get('telephone1')?.setValidators([Validators.required, Validators.pattern(/^(\+[0-9]{1,4})?[0-9]{8,15}$/)]);
    group.get('telephone2')?.setValidators([Validators.pattern(/^(\+[0-9]{1,4})?[0-9]{8,15}$/)]);
    // Email client pas obligatoire
    group.get('email')?.setValidators([Validators.email]);

    Object.keys(group.controls).forEach(key => {
      group.get(key)?.updateValueAndValidity();
    });
  }

  private clearNewClientValidators(group: FormGroup): void {
    Object.keys(group.controls).forEach(key => {
      group.get(key)?.clearValidators();
      group.get(key)?.updateValueAndValidity();
    });
  }



  getSelectedClientName(): string {
    const selectedClientId = this.step2Form.get('selectedClient')?.value;
    if (selectedClientId != null) {
      const client = this.existingClients.find(c => c.id === selectedClientId.toString());
      return client ? `${client.nom} ${client.prenom}` : 'Client sélectionné';
    }
    return 'Aucun client sélectionné';
  }

  private validateAllSteps(): boolean {
    const forms = [this.step1Form, this.step2Form, this.step3Form, this.step4Form, this.step5Form];
    return forms.every(form => {
      this.markFormGroupTouched(form);
      return form.valid;
    });
  }

  getFieldError(formGroup: FormGroup, fieldName: string): string {
    const field = formGroup.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return this.getRequiredMessage(fieldName);
      }
      if (field.errors['minlength']) {
        const requiredLength = field.errors['minlength'].requiredLength;
        if (fieldName === 'cin') {
          return `Le CIN doit contenir au moins ${requiredLength} caractères`;
        }
        return `${this.getFieldLabel(fieldName)} doit contenir au moins ${requiredLength} caractères`;
      }
      if (field.errors['email']) {
        return 'Format d\'email invalide';
      }
      if (field.errors['pattern']) {
        return this.getPatternMessage(fieldName);
      }
    }
    return '';
  }

  // Méthode pour forcer la mise à jour des erreurs
  forceErrorUpdate(): void {
    // Utiliser setTimeout pour s'assurer que les changements sont appliqués
    setTimeout(() => {
      const currentStepIndex = this.currentStep();

      // Pour l'étape 3, utiliser le bon FormGroup selon le type
      let step3FormToUse = this.step3Form;
      if (currentStepIndex === 2 && this.isLocationMateriel()) {
        step3FormToUse = this.step3LocationForm;
      }

      const forms = [this.step1Form, this.step2Form, step3FormToUse, this.step4Form, this.step5Form];

      if (currentStepIndex < forms.length) {
        const currentForm = forms[currentStepIndex];
        currentForm.updateValueAndValidity();

        if (currentStepIndex === 1) {
          const newClientForm = this.step2Form.get('newClient') as FormGroup;
          newClientForm.updateValueAndValidity();
        }
      }
    }, 0);
  }

  private getRequiredMessage(fieldName: string): string {
    const messages: { [key: string]: string } = {
      'category': 'La catégorie est requise',
      'eventType': 'Le type d\'événement est requis',
      'clientType': 'Le type de client est requis',
      'selectedClient': 'Veuillez sélectionner un client',
      'nom': 'Le nom est requis',
      'prenom': 'Le prénom est requis',
      'cin': 'Le CIN est requis',
      'ville': 'La ville est requise',
      'adresse': 'L\'adresse est requise',
      'telephone1': 'Le téléphone est requis',
      'email': 'L\'email est requis',
      'nbPersonnes': 'Le nombre de personnes est requis',
      'date': 'La date est requise',
      'statut': 'Le statut du paiement est requis',
      'montantTotal': 'Le montant total est requis',
      'montantPaye': 'Le montant payé est requis'
    };
    return messages[fieldName] || `${this.getFieldLabel(fieldName)} est requis`;
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      'category': 'La catégorie',
      'eventType': 'Le type d\'événement',
      'clientType': 'Le type de client',
      'selectedClient': 'Le client',
      'nom': 'Le nom',
      'prenom': 'Le prénom',
      'cin': 'Le CIN',
      'ville': 'La ville',
      'adresse': 'L\'adresse',
      'telephone1': 'Le téléphone',
      'telephone2': 'Le téléphone 2',
      'email': 'L\'email',
      'nbPersonnes': 'Le nombre de personnes',
      'date': 'La date',
      'statut': 'Le statut',
      'montantTotal': 'Le montant total',
      'montantPaye': 'Le montant payé'
    };
    return labels[fieldName] || fieldName;
  }

  private getPatternMessage(fieldName: string): string {
    const messages: { [key: string]: string } = {
      'cin': 'Le CIN doit contenir 1 à 3 lettres suivies de 4 à 6 chiffres',
      'telephone1': 'Format de téléphone invalide (ex: 0612345678 ou +212612345678)',
      'telephone2': 'Format de téléphone invalide (ex: 0612345678 ou +212612345678)'
    };
    return messages[fieldName] || 'Format invalide';
  }

  // Méthodes spécifiques pour éviter la logique dans le HTML
  getNewClientFieldError(fieldName: string): string {
    const newClientGroup = this.step2Form.get('newClient') as FormGroup;
    return this.getFieldError(newClientGroup, fieldName);
  }

  getStep1FieldError(fieldName: string): string {
    return this.getFieldError(this.step1Form, fieldName);
  }

  getStep2FieldError(fieldName: string): string {
    return this.getFieldError(this.step2Form, fieldName);
  }

  getStep3FieldError(fieldName: string): string {
    const currentForm = this.getCurrentStep3Form();
    return this.getFieldError(currentForm, fieldName);
  }

  getStep4FieldError(fieldName: string): string {
    return this.getFieldError(this.step4Form, fieldName);
  }

  getStep5FieldError(fieldName: string): string {
    return this.getFieldError(this.step5Form, fieldName);
  }

  private markCurrentStepAsTouched(): void {
    const currentStepIndex = this.currentStep();

    // Pour l'étape 3, utiliser le bon FormGroup selon le type
    let step3FormToUse = this.step3Form;
    if (currentStepIndex === 2 && this.isLocationMateriel()) {
      step3FormToUse = this.step3LocationForm;
    }

    const forms = [this.step1Form, this.step2Form, step3FormToUse, this.step4Form, this.step5Form];

    if (currentStepIndex < forms.length) {
      const currentForm = forms[currentStepIndex];
      this.markFormGroupTouched(currentForm);

      // Pour l'étape 2, gérer les deux cas : client existant et nouveau client
      if (currentStepIndex === 1) {
        if (this.isClientTypeNew) {
          const newClientForm = this.step2Form.get('newClient') as FormGroup;
          this.markFormGroupTouched(newClientForm);
        } else {
          // Marquer le champ selectedClient comme touché
          const selectedClientControl = this.step2Form.get('selectedClient');
          selectedClientControl?.markAsTouched();
          selectedClientControl?.markAsDirty();
          selectedClientControl?.updateValueAndValidity();
        }
      }
    }
  }

  loadEventData(eventId: string): void {
    // Simulation de données d'événement - à remplacer par un service réel
    const mockEventData = {
      id: eventId,
      category: 'particulier',
      eventType: 'mariage',
      clientType: 'existing',
      selectedClient: '1',
      newClient: {
        nom: '',
        prenom: '',
        cin: '',
        ville: '',
        adresse: '',
        telephone1: '',
        telephone2: '',
        email: ''
      },
      nbPersonnes: 150,
      date: new Date('2024-07-15T14:00:00'),
      ville: 'Casablanca',
      packs: ['Pack Premium'],
      photographe: true,
      photographeCelebre: false,
      dj: true,
      issawa: false,
      deqaMarrakchia: true,
      ftour: true,
      pieceMontee: true,
      decoration: true,
      decorationType: 'florale',
      typeMateriel: 'luxe',
      theme: 'Traditionnel marocain',
      statut: 'partiellement-paye',
      montantTotal: 25000,
      montantPaye: 15000,
      dateEcheance: new Date('2024-08-15'),
      methodePaiement: 'virement',
      sourceTraffic: 'bouche-a-oreille',
      commentaires: 'Mariage traditionnel avec 150 invités'
    };

    // Remplir les formulaires avec les données
    this.step1Form.patchValue({
      category: mockEventData.category,
      eventType: mockEventData.eventType
    });

    this.step2Form.patchValue({
      clientType: mockEventData.clientType,
      selectedClient: mockEventData.selectedClient,
      newClient: mockEventData.newClient
    });

    // Déclencher la validation du type de client pour activer les bons contrôles
    this.onClientTypeChange();

    // Vérifier que le client sélectionné existe dans la liste
    if (mockEventData.clientType === 'existing' && mockEventData.selectedClient != null) {
      const clientExists = this.existingClients.find(c => c.id === mockEventData.selectedClient.toString());
      if (!clientExists) {
        console.warn('Client sélectionné non trouvé dans la liste des clients existants');
        // Optionnel : réinitialiser la sélection si le client n'existe pas
        // this.step2Form.patchValue({ selectedClient: null });
      }
    }

    this.step3Form.patchValue({
      nbPersonnes: mockEventData.nbPersonnes,
      date: mockEventData.date,
      ville: mockEventData.ville,
      packs: mockEventData.packs,
      photographe: mockEventData.photographe,
      photographeCelebre: mockEventData.photographeCelebre,
      dj: mockEventData.dj,
      issawa: mockEventData.issawa,
      deqaMarrakchia: mockEventData.deqaMarrakchia,
      ftour: mockEventData.ftour,
      pieceMontee: mockEventData.pieceMontee,
      decoration: mockEventData.decoration,
      decorationType: mockEventData.decorationType,
      typeMateriel: mockEventData.typeMateriel,
      theme: mockEventData.theme
    });

    this.step4Form.patchValue({
      statut: mockEventData.statut,
      montantTotal: mockEventData.montantTotal,
      montantPaye: mockEventData.montantPaye,
      dateEcheance: mockEventData.dateEcheance,
      methodePaiement: mockEventData.methodePaiement
    });

    this.step5Form.patchValue({
      sourceTraffic: mockEventData.sourceTraffic,
      commentaires: mockEventData.commentaires
    });
  }

  setupTableCalculation(): void {
    // Écouter les changements sur nbPersonnes et personnesParTable
    this.step3Form.get('nbPersonnes')?.valueChanges.subscribe(() => {
      this.calculateNbTables();
      this.checkMaterialAvailability();
    });

    this.step3Form.get('personnesParTable')?.valueChanges.subscribe(() => {
      this.calculateNbTables();
      this.checkMaterialAvailability();
    });

    this.step3Form.get('tableCount')?.valueChanges.subscribe(() => {
      this.checkMaterialAvailability();
    });
  }

  calculateNbTables(): void {
    const nbPersonnes = this.step3Form.get('nbPersonnes')?.value;
    const personnesParTable = this.step3Form.get('personnesParTable')?.value;

    if (nbPersonnes && personnesParTable && nbPersonnes > 0 && personnesParTable > 0) {
      this.nbTablesCalculated = Math.ceil(nbPersonnes / personnesParTable);
    } else {
      this.nbTablesCalculated = 0;
    }
  }

  get nbTablesCalculated(): number {
    return this._nbTablesCalculated;
  }

  set nbTablesCalculated(value: number) {
    this._nbTablesCalculated = value;
  }

  private _nbTablesCalculated: number = 0;

  checkPreselectedDate(): void {
    // Récupérer les paramètres date et time de l'URL
    const dateParam = this.route.snapshot.queryParamMap.get('date');
    const timeParam = this.route.snapshot.queryParamMap.get('time');

    if (dateParam) {
      this.isFromCalendar = true;

      try {
        // Convertir la date string en Date object
        const selectedDate = new Date(dateParam);

        // Vérifier que la date est valide
        if (!isNaN(selectedDate.getTime())) {
          // Gérer l'heure
          if (timeParam) {
            const [hours, minutes] = timeParam.split(':').map(Number);
            selectedDate.setHours(hours, minutes, 0, 0);
          } else {
            // Heure par défaut à 14:00
            selectedDate.setHours(14, 0, 0, 0);
          }

          // Stocker la date pré-sélectionnée
          this.preselectedDate = selectedDate;

          // Pré-remplir le champ date dans step3Form
          this.step3Form.patchValue({
            date: selectedDate
          });

          // Afficher un message de confirmation
          setTimeout(() => {
            this.message.info(`Date pré-sélectionnée : ${selectedDate.toLocaleDateString('fr-FR')} à ${selectedDate.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`);
          }, 500);

          console.log('Date pré-sélectionnée depuis le calendrier:', selectedDate);
        }
      } catch (error) {
        console.warn('Erreur lors du parsing de la date pré-sélectionnée:', error);
      }
    }
  }

  getFormattedPreselectedDate(): string {
    if (!this.preselectedDate) return '';

    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };

    return this.preselectedDate.toLocaleDateString('fr-FR', options);
  }

  onSubmit(): void {
    if (this.validateAllSteps()) {
      const formData = {
        step1: this.step1Form.value,
        step2: this.step2Form.value,
        step3: this.step3Form.value,
        step4: this.step4Form.value,
        step5: this.step5Form.value
      };

      console.log('Données du formulaire:', formData);

      if (this.isEditMode) {
        console.log('Modification de l\'événement ID:', this.eventId);
        // Ici, vous appelleriez votre service pour modifier l'événement
        alert('Événement modifié avec succès !');
      } else {
        console.log('Création d\'un nouvel événement');
        // Ici, vous appelleriez votre service pour créer l'événement
        alert('Événement créé avec succès !');
      }

      // Rediriger vers la liste des événements
      this.router.navigate(['/events']);
    } else {
      alert('Veuillez remplir tous les champs obligatoires.');
    }
  }

  // Méthodes pour la gestion des clients
  private loadExistingClients(): void {
    // Cette méthode sera remplacée par un appel au service des clients
    // Pour l'instant, les clients sont déjà définis dans la propriété existingClients
    console.log('Clients chargés:', this.existingClients.length);
  }

  // Méthodes pour la gestion des matériels
  private loadAvailableMaterials(): void {
    // Simulation des données de matériels depuis l'inventaire
    this.availableMaterials = [
      { id: '1', name: 'Chapiteaux', description: 'Tentes de réception', quantity: 5, unit: 'unité(s)', category: 'Structure' },
      { id: '2', name: 'Estrade', description: 'Plateformes surélevées', quantity: 3, unit: 'unité(s)', category: 'Structure' },
      { id: '3', name: 'Tables rondes', description: 'Tables de 8 personnes', quantity: 50, unit: 'unité(s)', category: 'Mobilier' },
      { id: '4', name: 'Chaises', description: 'Chaises de réception', quantity: 400, unit: 'unité(s)', category: 'Mobilier' },
      { id: '5', name: 'Assiettes', description: 'Assiettes en porcelaine', quantity: 500, unit: 'unité(s)', category: 'Vaisselle' },
      { id: '6', name: 'Cuillères', description: 'Cuillères en inox', quantity: 500, unit: 'unité(s)', category: 'Vaisselle' },
      { id: '7', name: 'Nappes', description: 'Nappes blanches', quantity: 60, unit: 'unité(s)', category: 'Linge' },
      { id: '8', name: 'Éclairage LED', description: 'Systèmes d\'éclairage', quantity: 15, unit: 'kit(s)', category: 'Technique' }
    ];
  }

  addMaterial(): void {
    const currentForm = this.getCurrentStep3Form();
    const selectedMaterialId = currentForm.get('selectedMaterialId')?.value;
    const materialQuantity = currentForm.get('materialQuantity')?.value;
    const materialUnit = currentForm.get('materialUnit')?.value;

    if (!selectedMaterialId || !materialQuantity || materialQuantity <= 0) {
      this.message.warning('Veuillez sélectionner un matériel et saisir une quantité valide');
      return;
    }

    const material = this.availableMaterials.find(m => m.id === selectedMaterialId);
    if (!material) {
      this.message.error('Matériel non trouvé');
      return;
    }

    // Vérifier si le matériel est déjà ajouté avec la même unité
    const existingIndex = this.eventMaterials.findIndex(m =>
      m.materialId === selectedMaterialId && m.unit === materialUnit
    );

    if (existingIndex > -1) {
      // Écraser l'entrée existante avec les nouvelles valeurs
      this.eventMaterials[existingIndex] = {
        materialId: material.id,
        materialName: material.name,
        quantity: materialQuantity,
        unit: materialUnit,
        availableQuantity: material.quantity,
        isOverLimit: false
      };
      this.message.success(`Matériel "${material.name}" mis à jour avec succès`);
    } else {
      // Ajouter nouveau matériel
      const eventMaterial: EventMaterial = {
        materialId: material.id,
        materialName: material.name,
        quantity: materialQuantity,
        unit: materialUnit,
        availableQuantity: material.quantity,
        isOverLimit: false
      };
      this.eventMaterials.push(eventMaterial);
      this.message.success(`Matériel "${material.name}" ajouté avec succès`);
    }

    this.checkMaterialAvailability();
    this.resetMaterialForm();
  }

  removeMaterial(index: number): void {
    this.eventMaterials.splice(index, 1);
    this.checkMaterialAvailability();
  }

  private checkMaterialAvailability(): void {
    this.eventMaterials.forEach(eventMaterial => {
      const totalNeeded = this.calculateTotalQuantity(eventMaterial);
      eventMaterial.isOverLimit = totalNeeded > eventMaterial.availableQuantity;
    });
  }

  private calculateTotalQuantity(eventMaterial: EventMaterial): number {
    if (eventMaterial.unit === 'table' && !this.isLocationMateriel()) {
      const tableCount = this.nbTablesCalculated || 0;
      return eventMaterial.quantity * tableCount;
    } else if (eventMaterial.unit === 'personne' && !this.isLocationMateriel()) {
      const nbPersonnes = this.step3Form.get('nbPersonnes')?.value || 0;
      return eventMaterial.quantity * nbPersonnes;
    }
    return eventMaterial.quantity;
  }

  private resetMaterialForm(): void {
    const currentForm = this.getCurrentStep3Form();
    const defaultUnit = this.isLocationMateriel() ? 'unite' : 'table';
    currentForm.patchValue({
      selectedMaterialId: '',
      materialQuantity: 1,
      materialUnit: defaultUnit
    });
  }

  isAddMaterialDisabled(): boolean {
    const currentForm = this.getCurrentStep3Form();
    if (!currentForm) return true;

    const selectedMaterialId = currentForm.get('selectedMaterialId')?.value;
    const materialQuantity = currentForm.get('materialQuantity')?.value;

    console.log('isAddMaterialDisabled debug:', {
      selectedMaterialId,
      materialQuantity,
      disabled: !selectedMaterialId || !materialQuantity || materialQuantity <= 0
    });

    return !selectedMaterialId || !materialQuantity || materialQuantity <= 0;
  }

  getTotalQuantityText(material: EventMaterial): string {
    const total = this.calculateTotalQuantity(material);
    let unit = '';
    switch (material.unit) {
      case 'table':
        unit = 'par table';
        break;
      case 'personne':
        unit = 'par personne';
        break;
      default:
        unit = 'pour l\'événement';
    }
    return `${material.quantity} ${unit} = ${total} au total`;
  }

  getAvailabilityText(material: EventMaterial): string {
    const total = this.calculateTotalQuantity(material);
    const available = material.availableQuantity;
    const remaining = available - total;

    if (material.isOverLimit) {
      return `⚠️ Dépassement de ${total - available} unités`;
    }
    return `✅ ${remaining} restantes en stock`;
  }

  isLocationMateriel(): boolean {
    return this.step1Form.get('category')?.value === 'location-materiel';
  }

  getCurrentStep3Form(): FormGroup {
    return this.isLocationMateriel() ? this.step3LocationForm : this.step3Form;
  }

  // Méthodes pour l'autocomplete des clients
  onClientSearch(value: string): void {
    this.clientSearchValue = value;
    this.filteredClients = this.existingClients.filter(client =>
      `${client.nom} ${client.prenom}`.toLowerCase().includes(value.toLowerCase()) ||
      client.cin.toLowerCase().includes(value.toLowerCase())
    );
  }

  onClientSelect(client: Client): void {
    this.step2Form.patchValue({
      selectedClient: client.id
    });
    this.clientSearchValue = `${client.nom} ${client.prenom}`;
    // Marquer le champ comme touché pour déclencher la validation
    this.step2Form.get('selectedClient')?.markAsTouched();
    this.step2Form.get('selectedClient')?.updateValueAndValidity();
  }

  // Méthode pour le tooltip de calcul des tables
  getTableCalculationTooltip(): string {
    const nbPersonnes = this.step3Form.get('nbPersonnes')?.value || 0;
    const personnesParTable = this.step3Form.get('personnesParTable')?.value || 8;

    if (nbPersonnes === 0 || personnesParTable === 0) {
      return '';
    }

    const reste = nbPersonnes % personnesParTable;
    if (reste === 0) {
      return '✅ Division exacte - toutes les tables seront complètes';
    } else {
      return `⚠️ Il restera ${reste} personne(s) - une table ne sera pas complète`;
    }
  }
}

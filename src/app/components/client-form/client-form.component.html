<div class="client-form-container">
  <!-- Header avec titre -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">{{ isEditMode ? 'Modifier le client' : 'Nouveau client' }}</h2>
    </div>
  </div>

  <nz-card class="client-form-card">
    <form [formGroup]="clientForm" (ngSubmit)="onSubmit()" nz-form nzLayout="vertical">
    
    <!-- Nom et Prénom -->
    <div nz-row [nzGutter]="16">
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzRequired>Nom</nz-form-label>
          <nz-form-control [nzErrorTip]="nomError || ''">
            <input 
              nz-input 
              formControlName="nom" 
              placeholder="Nom de famille"
              [class.error]="nomError" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzRequired>Prénom</nz-form-label>
          <nz-form-control [nzErrorTip]="prenomError || ''">
            <input
              nz-input
              formControlName="prenom"
              placeholder="Prénom"
              [class.error]="prenomError" />
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <!-- CIN et Ville -->
    <div nz-row [nzGutter]="16">
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label>CIN</nz-form-label>
          <nz-form-control [nzErrorTip]="cinError || ''">
            <input
              nz-input
              formControlName="cin"
              placeholder="CIN (optionnel)"
              [class.error]="cinError"
              style="text-transform: uppercase;" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzRequired>Ville</nz-form-label>
          <nz-form-control [nzErrorTip]="villeError || ''">
            <input
              nz-input
              [(ngModel)]="citySearchValue"
              [ngModelOptions]="{standalone: true}"
              (ngModelChange)="onCitySearch($event)"
              placeholder="Ville de résidence"
              [nzAutocomplete]="cityAutocomplete">
            <nz-autocomplete #cityAutocomplete>
              @for (city of filteredCities; track city) {
                <nz-auto-option [nzValue]="city" (click)="onCitySelect(city)">
                  {{ city }}
                </nz-auto-option>
              }
            </nz-autocomplete>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <!-- Adresse -->
    <nz-form-item>
      <nz-form-label>Adresse</nz-form-label>
      <nz-form-control [nzErrorTip]="adresseError || ''">
        <textarea
          nz-input
          formControlName="adresse"
          placeholder="Adresse complète (optionnel)"
          rows="2"
          [class.error]="adresseError">
        </textarea>
      </nz-form-control>
    </nz-form-item>

    <!-- Téléphones -->
    <div nz-row [nzGutter]="16">
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzRequired>Téléphone principal</nz-form-label>
          <nz-form-control [nzErrorTip]="telephone1Error || ''">
            <input
              nz-input
              formControlName="telephone1"
              placeholder="Téléphone principal"
              [class.error]="telephone1Error" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label>Téléphone secondaire</nz-form-label>
          <nz-form-control [nzErrorTip]="telephone2Error || ''">
            <input
              nz-input
              formControlName="telephone2"
              placeholder="Téléphone secondaire"
              [class.error]="telephone2Error" />
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <!-- Email -->
    <nz-form-item>
      <nz-form-label>Email</nz-form-label>
      <nz-form-control [nzErrorTip]="emailError || ''">
        <input
          nz-input
          formControlName="email"
          placeholder="Adresse email (optionnel)"
          type="email"
          [class.error]="emailError" />
      </nz-form-control>
    </nz-form-item>

    <!-- Actions -->
    <div class="form-actions">
      <button 
        nz-button 
        nzType="default" 
        type="button"
        [disabled]="isSubmitting">
        Annuler
      </button>
      
      <button 
        nz-button 
        nzType="primary" 
        type="submit"
        [nzLoading]="isSubmitting"
        [disabled]="!clientForm.valid">
        <nz-icon nzType="save" *ngIf="!isSubmitting"></nz-icon>
        {{ isSubmitting ? 'Création...' : 'Créer le client' }}
      </button>
    </div>

    <!-- Aide -->
    <div class="form-help">
      <h4>Aide pour le remplissage :</h4>
      <ul>
        <li><strong>CIN :</strong> Optionnel. Format attendu : 1-3 lettres suivies de 4-6 chiffres (ex: AB1234, ABC123456)</li>
        <li><strong>Téléphone :</strong> Support international (ex: 0612345678 ou +212612345678)</li>
        <li><strong>Email :</strong> Optionnel. Adresse email valide pour les communications</li>
        <li><strong>Adresse :</strong> Optionnelle pour le client</li>
      </ul>
    </div>
    </form>
  </nz-card>
</div>

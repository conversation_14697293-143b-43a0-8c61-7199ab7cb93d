import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';

export interface Client {
  id?: string;
  nom: string;
  prenom: string;
  cin: string;
  ville: string;
  adresse: string;
  telephone1: string;
  telephone2?: string;
  email: string;
}

@Component({
  selector: 'app-client-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzIconModule,
    NzCardModule,
    NzAutocompleteModule
  ],
  templateUrl: './client-form.component.html',
  styleUrl: './client-form.component.css'
})
export class ClientFormComponent {
  @Output() clientCreated = new EventEmitter<Client>();

  clientForm!: FormGroup;
  isSubmitting = false;

  // Autocomplete pour villes
  availableCities: string[] = [
    'Casablanca', 'Rabat', 'Fès', 'Marrakech', 'Agadir', 'Tanger', 'Meknès', 'Oujda',
    'Kenitra', 'Tétouan', 'Safi', 'Mohammedia', 'Khouribga', 'Beni Mellal', 'El Jadida',
    'Nador', 'Taza', 'Settat', 'Berrechid', 'Khemisset', 'Inezgane', 'Ksar El Kebir',
    'Larache', 'Guelmim', 'Berkane', 'Taourirt', 'Bouskoura', 'Fquih Ben Salah',
    'Dcheira El Jihadia', 'Oued Zem', 'Sidi Slimane', 'Errachidia', 'Guercif',
    'Ait Melloul', 'Laâyoune', 'Tiznit', 'Tan-Tan', 'Ouarzazate', 'Sidi Kacem',
    'Khenifra', 'Lqliaa', 'Taroudant', 'Sefrou', 'Essaouira', 'Fnideq', 'Sidi Bennour'
  ];
  filteredCities: string[] = [];
  citySearchValue = '';
  isEditMode = false; // Pour le titre de la card

  constructor(
    private fb: FormBuilder,
    private message: NzMessageService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.filteredCities = [...this.availableCities];
  }

  private initializeForm(): void {
    this.clientForm = this.fb.group({
      nom: ['', [Validators.required, Validators.minLength(2)]],
      prenom: ['', [Validators.required, Validators.minLength(2)]],
      // CIN: optionnel, mais si rempli doit respecter le format 1-3 lettres + 4-6 chiffres
      cin: ['', [Validators.pattern(/^[A-Z]{1,3}[0-9]{4,6}$/), Validators.minLength(6)]],
      ville: ['', [Validators.required, Validators.minLength(2)]],
      // Adresse client pas obligatoire
      adresse: ['', [Validators.minLength(5)]],
      // Téléphone avec support international (+...)
      telephone1: ['', [Validators.required, Validators.pattern(/^(\+[0-9]{1,4})?[0-9]{8,15}$/)]],
      telephone2: ['', [Validators.pattern(/^(\+[0-9]{1,4})?[0-9]{8,15}$/)]],
      // Email client pas obligatoire
      email: ['', [Validators.email]]
    });
  }

  onSubmit(): void {
    if (this.clientForm.valid) {
      this.isSubmitting = true;

      // Simulation d'un appel API
      setTimeout(() => {
        const newClient: Client = {
          id: this.generateId(),
          ...this.clientForm.value
        };

        this.clientCreated.emit(newClient);
        this.message.success('Client créé avec succès !');
        this.resetForm();
        this.isSubmitting = false;
      }, 1000);
    } else {
      this.markFormGroupTouched();
      this.message.error('Veuillez corriger les erreurs dans le formulaire');
      this.scrollToTop();
    }
  }

  // Méthode pour scroll vers le haut
  private scrollToTop(): void {
    // Essayer de scroller le document principal
    document.documentElement.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    // Fallback pour window
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    // Fallback pour body
    document.body.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  private generateId(): string {
    return 'client_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.clientForm.controls).forEach(key => {
      const control = this.clientForm.get(key);
      control?.markAsTouched();
    });
  }

  private resetForm(): void {
    this.clientForm.reset();
    Object.keys(this.clientForm.controls).forEach(key => {
      const control = this.clientForm.get(key);
      control?.markAsUntouched();
      control?.markAsPristine();
    });
  }

  // Getters pour les validations
  get nomError(): string | null {
    const control = this.clientForm.get('nom');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'Le nom est requis';
      if (control.errors['minlength']) return 'Le nom doit contenir au moins 2 caractères';
    }
    return null;
  }

  get prenomError(): string | null {
    const control = this.clientForm.get('prenom');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'Le prénom est requis';
      if (control.errors['minlength']) return 'Le prénom doit contenir au moins 2 caractères';
    }
    return null;
  }

  get cinError(): string | null {
    const control = this.clientForm.get('cin');
    if (control?.touched && control?.errors) {
      if (control.errors['pattern']) return 'Le CIN doit contenir 1 à 3 lettres suivies de 4 à 6 chiffres';
      if (control.errors['minlength']) return 'Le CIN doit contenir au moins 6 caractères';
    }
    return null;
  }

  get villeError(): string | null {
    const control = this.clientForm.get('ville');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'La ville est requise';
      if (control.errors['minlength']) return 'La ville doit contenir au moins 2 caractères';
    }
    return null;
  }

  get adresseError(): string | null {
    const control = this.clientForm.get('adresse');
    if (control?.touched && control?.errors) {
      if (control.errors['minlength']) return 'L\'adresse doit contenir au moins 5 caractères';
    }
    return null;
  }

  get telephone1Error(): string | null {
    const control = this.clientForm.get('telephone1');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'Le téléphone principal est requis';
      if (control.errors['pattern']) return 'Format de téléphone invalide (ex: 0612345678 ou +212612345678)';
    }
    return null;
  }

  get telephone2Error(): string | null {
    const control = this.clientForm.get('telephone2');
    if (control?.touched && control?.errors) {
      if (control.errors['pattern']) return 'Format de téléphone invalide (ex: 0612345678 ou +212612345678)';
    }
    return null;
  }

  get emailError(): string | null {
    const control = this.clientForm.get('email');
    if (control?.touched && control?.errors) {
      if (control.errors['email']) return 'Format email invalide';
    }
    return null;
  }

  // Méthodes pour l'autocomplete des villes
  onCitySearch(value: string): void {
    if (!value) {
      this.filteredCities = [...this.availableCities];
      return;
    }

    this.filteredCities = this.availableCities.filter(city =>
      city.toLowerCase().includes(value.toLowerCase())
    );
  }

  onCitySelect(city: string): void {
    this.clientForm.patchValue({ ville: city });
    this.citySearchValue = city;

    // S'assurer que le contrôle est bien mis à jour
    const villeControl = this.clientForm.get('ville');
    if (villeControl) {
      villeControl.setValue(city);
      villeControl.markAsTouched();
      villeControl.markAsDirty();
      villeControl.updateValueAndValidity();
    }
  }
}
